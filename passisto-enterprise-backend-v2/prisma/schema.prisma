// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Enable multiSchema preview feature for @@schema support
generator client {
  provider        = "prisma-client-js"
  // output       = "../generated/prisma"
  previewFeatures = ["multiSchema"]
}

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["public", "workflow"]
}

enum ProviderType {
  jira
  web
  ftp

  @@schema("public")
}

enum IntegrationStatus {
  loading
  completed
  failed
  refreshing
  not_updated

  @@schema("public")
}

model Company {
  id          String       @id @default(uuid())
  name        String       @unique
  description String?
  users       User[]
  invitations Invitation[]
  forms       Form[]
  emails      Email[]
  groups      Group[]
  integrations Integration[]

  stripeCustomerId     String?       @unique
  stripeSubscriptionId String?
  subscriptionStatus   String?
  subscription         Subscription? @relation(fields: [subscriptionId], references: [id])
  subscriptionId       String?
  usage         CompanyUsage[]

  // emailAgentsUsed      Int   @default(0)
  // aiInterviewHoursUsed Float @default(0)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@schema("public")
}

model Subscription {
  id                  String @id @default(uuid())
  name                String
  stripeProductId     String @unique
  // maxEmailAgents      Int
  // maxAiInterviewHours Float
  features        SubscriptionFeature[]

  companies Company[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@schema("public")
}

model SubscriptionFeature {
  id              String       @id @default(uuid())
  subscription    Subscription @relation(fields: [subscriptionId], references: [id])
  subscriptionId  String

  key             String       // e.g., "maxEmailAgents", "maxAiInterviewHours"
  value           Float        // Store limits like 10, 100, 50.5 etc.

  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt

  @@unique([subscriptionId, key]) // Prevent duplicate keys for same subscription

  @@schema("public")
}

model CompanyUsage {
  id         String   @id @default(uuid())
  company    Company  @relation(fields: [companyId], references: [id])
  companyId  String

  key        String   // e.g., "emailAgentsUsed", "aiInterviewHoursUsed"
  value      Float    // Store usage values (0, 15, 22.5, etc.)

  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@unique([companyId, key]) // Prevent duplicate usage key per company

  @@schema("public")
}

model User {
  id              String  @id @default(uuid())
  clerkId         String  @unique
  email           String  @unique
  password        String?
  isActive        Boolean @default(true)
  firstName       String
  lastName        String
  bio             String?
  companyId       String
  roleInCompany   String?
  areaOfInterest  String?
  linkedinProfile String?
  hasCompletedOnboarding Boolean @default(false)

  company Company @relation(fields: [companyId], references: [id])
  roles   Role[]

  groups    UserGroup[]
  overrides UserOverride?
  
  workflows Workflow[]
  nodeRuns  NodeRun[]  // Nodes assigned to this user

  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  @@schema("public")
}

model Invitation {
  id        String   @id @default(uuid())
  email     String   @unique
  companyId String
  createdAt DateTime @default(now())

  company Company @relation(fields: [companyId], references: [id])

  @@schema("public")
}

model Role {
  id          String           @id @default(uuid())
  name        String           @unique
  users       User[]
  permissions RolePermission[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@schema("public")
}

enum ScopeType {
  GLOBAL
  TEAM
  PROJECT
  SELF

  @@schema("public")
}

model RolePermission {
  id           String     @id @default(uuid())
  roleId       String
  permissionId String
  scopeType    ScopeType  @default(GLOBAL)
  scopeId      String
  role         Role       @relation(fields: [roleId], references: [id])
  permission   Permission @relation(fields: [permissionId], references: [id])

  @@schema("public")
}

model Group {
  id          String            @id @default(uuid())
  name        String
  description String
  companyId   String
  company     Company           @relation(fields: [companyId], references: [id])
  users       UserGroup[]
  permissions GroupPermission[]

  opensearchAliasId     String @unique @default(uuid())
  integrations          GroupIntegration[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@schema("public")
}


model GroupIntegration {
  groupId       String
  integrationId String
  assignedAt    DateTime @default(now())

  group         Group      @relation(fields: [groupId], references: [id])
  integration   Integration @relation(fields: [integrationId], references: [id])

  @@id([groupId, integrationId])

  @@schema("public")
}

model GroupPermission {
  id           String    @id @default(uuid())
  groupId      String
  permissionId String
  scopeType    ScopeType @default(TEAM)
  scopeId      String // ressource (alisa:Id), (interview:id)

  group      Group      @relation(fields: [groupId], references: [id])
  permission Permission @relation(fields: [permissionId], references: [id])

  @@schema("public")
}

model UserGroup {
  id      String @id @default(uuid())
  userId  String
  groupId String

  user  User  @relation(fields: [userId], references: [id])
  group Group @relation(fields: [groupId], references: [id])

  @@schema("public")
}

model UserOverride {
  id                 String               @id @default(uuid())
  userId             String               @unique
  user               User                 @relation(fields: [userId], references: [id])
  extraPermissions   PermissionOverride[] @relation("ExtraPermissions")
  revokedPermissions PermissionOverride[] @relation("RevokedPermissions")

  @@schema("public")
}

model PermissionOverride {
  id                String        @id @default(uuid())
  permissionId      String
  scopeType         ScopeType
  scopeId           String
  extraOverrideId   String?
  extraOverride     UserOverride? @relation("ExtraPermissions", fields: [extraOverrideId], references: [id])
  revokedOverrideId String?
  revokedOverride   UserOverride? @relation("RevokedPermissions", fields: [revokedOverrideId], references: [id])
  permission        Permission    @relation(fields: [permissionId], references: [id])

  @@schema("public")
}

model Permission {
  id                 String               @id @default(uuid())
  action             String               @unique
  category           String
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  RolePermission     RolePermission[]
  GroupPermission    GroupPermission[]
  PermissionOverride PermissionOverride[]

  @@schema("public")
}

// AI Agents

model Form {
  id          String  @id @default(uuid())
  title       String
  description String?
  fields      Json
  brandColor  String  @default("#4f46e5")
  logo        String?
  companyId   String
  company     Company @relation(fields: [companyId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  responses FormResponse?

  @@schema("public")
}

model FormResponse {
  id          String   @id @default(uuid())
  formId      String   @unique
  form        Form     @relation(fields: [formId], references: [id], onDelete: Cascade)
  responses   Json     // Les réponses du formulaire
  submittedAt DateTime @default(now())

  @@map("FormResponse")

  @@schema("public")
}

model Email {
  id          String   @id @default(uuid())
  title       String
  description String?
  fields      Json
  html        String?
  companyId   String
  company     Company  @relation(fields: [companyId], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@schema("public")
}

model Integration {
  id           String            @id @default(uuid())
  name         String            @unique
  updateTime   Int               @default(2)
  status       IntegrationStatus @default(loading)
  opensearchIndexId  String
  celeryTaskId    String   
  providerType ProviderType
  firstLoad     Boolean          @default(true)

  createdBy    String

  companyId    String
  company      Company           @relation(fields: [companyId], references: [id])


  groups       GroupIntegration[]

  // Relations to specific integration types
  jira         JiraIntegration?
  ftp          FtpIntegration?
  web          WebIntegration?

  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt

  @@schema("public")
}

model JiraIntegration {
  id           String        @id @default(uuid())
  integration  Integration   @relation(fields: [id], references: [id])
  domain       String
  project      String
  email        String
  encryptedToken    String

  @@schema("public")
}

model FtpIntegration {
  id           String        @id @default(uuid())
  integration  Integration   @relation(fields: [id], references: [id])
  server       String
  port         Int
  username     String
  encryptedPassword     String
  isSecure     Boolean

  @@schema("public")
}

model WebIntegration {
  id           String        @id @default(uuid())
  integration  Integration   @relation(fields: [id], references: [id])
  url          String

  @@schema("public")
}

model DemoRequest {
  id        String   @id @default(uuid())
  firstName String
  lastName  String
  company   String
  position  String
  email     String
  message   String
  createdAt DateTime @default(now())
  
  @@schema("public")
}

model OTPVerification {
  id        String   @id @default(uuid())
  email     String
  otp       Int
  expiresAt DateTime
  isVerified Boolean @default(false)
  createdAt DateTime @default(now())

  @@schema("public")
}

// WORKFLOW SCHEMA - All workflow-related tables

model Workflow {
  id          String        @id @default(cuid())
  name        String
  description String?
  nodes       Json
  edges       Json
  viewport    Json          @default("{\"x\": 0, \"y\": 0, \"zoom\": 1.0}")
  status      Status        @default(draft)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  userId      String
  user        User          @relation(fields: [userId], references: [id])
  runs        WorkflowRun[]

  @@schema("workflow")
}

model WorkflowRun {
  id         String         @id @default(cuid())
  workflowId String
  status     WorkflowStatus @default(PENDING)
  startedAt  DateTime?
  finishedAt DateTime?
  createdAt  DateTime       @default(now())
  // Store execution state for crash recovery
  executionState Json?      // { startTime, executionPath, currentNodeId, nodes, edges }
  nodeRuns   NodeRun[]
  workflow   Workflow       @relation(fields: [workflowId], references: [id])

  @@schema("workflow")
}

model NodeRun {
  id            String      @id @default(cuid())
  workflowRunId String
  nodeId        String
  status        NodeStatus  @default(PENDING)
  startedAt     DateTime?
  finishedAt    DateTime?
  output        Json?
  formData      Json?       // Stores form field data for task nodes
  assigneeId    String?     // User ID assigned to this node
  assignedAt    DateTime?   // When the node was assigned
  completedBy   String?     // User ID who completed the node
  workflowRun   WorkflowRun @relation(fields: [workflowRunId], references: [id])
  assignee      User?       @relation(fields: [assigneeId], references: [id])
  files         File[]      // Files attached to this node run

  @@unique([workflowRunId, nodeId])
  @@schema("workflow")
}

enum Status {
  draft
  active
  archived

  @@schema("workflow")
}

enum WorkflowStatus {
  PENDING
  RUNNING
  WAITING_FOR_USER
  SUCCESS
  FAILED

  @@schema("workflow")
}

enum NodeStatus {
  PENDING
  RUNNING
  WAITING_FOR_USER
  SUCCESS
  FAILED
  SKIPPED

  @@schema("workflow")
}

model File {
  id          String    @id @default(cuid())
  filename    String
  originalName String
  mimetype    String
  size        Int
  path        String    // Server path where file is stored
  url         String?   // Public URL if available
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  nodeRunId   String?   // Optional - used when file is attached to a running node
  nodeRun     NodeRun?  @relation(fields: [nodeRunId], references: [id], onDelete: Cascade)
  nodeId      String?   // Optional - used when file is attached to a node before execution
  uploadedBy  String    // User ID who uploaded the file

  @@schema("workflow")
}