# User-Specific Socket Events

This document explains how the workflow designer implements user-specific socket events to ensure that users only receive events related to their own workflows.

## Overview

Previously, all socket events were broadcast to all connected clients, which meant that users could see workflow progress updates from other users' workflows. This implementation adds user authentication and filtering to ensure that:

1. Only authenticated users can connect to the socket server
2. Users only receive events for workflows they own
3. Multiple users can use the workflow designer simultaneously without interference

## Architecture

### Backend Changes

#### 1. Socket Authentication (`src/lib/socketHandler.js`)

- Added JWT token verification in socket middleware
- Users must provide a valid Clerk authentication token when connecting
- Socket connections are mapped to user IDs for event filtering
- Unauthorized connections are rejected

#### 2. User-Specific Event Emission

Three new helper functions were added to the socket handler:

```javascript
// Emit events to a specific user
io.emitToUser(userId, eventName, data)

// Emit events to the owner of a specific workflow
io.emitToWorkflowOwner(workflowId, eventName, data)

// Emit events to the owner of a specific workflow run
io.emitToWorkflowRunOwner(workflowRunId, eventName, data)
```

#### 3. Updated Event Emitters

The following services were updated to use user-specific emission:

- `BaseNodeExecutor.js` - Node execution and completion events
- `TaskNodeExecutor.js` - Task waiting events
- `workflowExecutionService.js` - Workflow progress and completion events

### Frontend Changes

#### 1. Socket Service Authentication (`src/services/socket.js`)

- Added `setAuthToken()` method to store authentication tokens
- Modified `connect()` method to accept and use authentication tokens
- Socket connections now include the user's Clerk JWT token

#### 2. Workflow Socket Hook (`src/components/workflows/workflow-designer/use-workflow-socket.ts`)

- Integrated with Clerk's `useAuth()` hook to get authentication tokens
- Automatically provides authentication when connecting to the socket server

## Event Flow

1. **User Authentication**: When a user opens the workflow designer, their Clerk JWT token is obtained
2. **Socket Connection**: The frontend connects to the socket server with the authentication token
3. **Server Verification**: The backend verifies the token and associates the socket with the user ID
4. **Event Filtering**: When workflow events occur, they are only sent to the user who owns the workflow
5. **Client Updates**: Only the relevant user receives and processes the events

## Supported Events

The following events are now user-specific:

- `workflowRunProgress` - Workflow execution progress updates
- `nodeRunProgress` - Individual node execution progress
- `nodeExecution` - Node execution details
- `workflowCompleted` - Workflow completion notifications
- `decisionNodeResult` - Decision node results

## Security Considerations

1. **Token Validation**: All socket connections require valid Clerk JWT tokens
2. **Ownership Verification**: Events are only sent to users who own the related workflows
3. **Database Queries**: Workflow ownership is verified through database queries
4. **Fallback Behavior**: If user-specific emission fails, events fall back to broadcast (for backward compatibility)

## Testing

A test script is provided at `test-user-specific-sockets.js` to verify the implementation:

```bash
node test-user-specific-sockets.js
```

This script:
- Simulates multiple user connections
- Verifies connection isolation
- Monitors event reception
- Reports results

## Usage Example

### Frontend (React Component)

```typescript
import { useAuth } from "@clerk/nextjs";
import socketService from "@/services/socket";

function WorkflowDesigner() {
  const { getToken } = useAuth();

  useEffect(() => {
    const connectWithAuth = async () => {
      const token = await getToken();
      socketService.connect(token);
    };
    
    connectWithAuth();
  }, [getToken]);

  // ... rest of component
}
```

### Backend (Event Emission)

```javascript
// Emit to workflow owner
this.io.emitToWorkflowRunOwner(workflowRunId, 'nodeRunProgress', {
  workflowRunId,
  nodeId,
  status: 'SUCCESS',
  message: 'Node completed successfully'
});
```

## Migration Notes

- Existing socket connections without authentication will still work (fallback to broadcast)
- No breaking changes to the frontend API
- Backward compatibility maintained for all existing functionality
- Gradual migration possible by updating components one at a time

## Troubleshooting

### Common Issues

1. **Authentication Failures**: Ensure Clerk tokens are valid and not expired
2. **Missing Events**: Verify that the user owns the workflow being executed
3. **Connection Issues**: Check that the socket server is running and accessible

### Debug Logging

Enable debug logging to troubleshoot issues:

```javascript
// Backend
console.debug(`Emitted ${event} to user ${userId} (${socketIds.size} sockets)`);

// Frontend
console.log('Connecting with authentication token');
```
