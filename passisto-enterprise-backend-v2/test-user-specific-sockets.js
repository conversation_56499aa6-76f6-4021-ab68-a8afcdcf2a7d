/**
 * Test script to verify user-specific socket events
 * This script simulates multiple users connecting and verifies that events are only sent to the correct users
 */

const { io } = require('socket.io-client');
const jwt = require('jsonwebtoken');

// Mock JWT tokens for testing (in real scenario, these would be valid Clerk tokens)
const createMockToken = (clerkUserId) => {
  return jwt.sign({ sub: clerkUserId }, 'mock-secret', { expiresIn: '1h' });
};

// Test configuration
const SOCKET_URL = process.env.SOCKET_URL || 'http://localhost:5000';
const TEST_USERS = [
  { clerkId: 'user_test1', name: 'Test User 1' },
  { clerkId: 'user_test2', name: 'Test User 2' }
];

class SocketTester {
  constructor() {
    this.connections = new Map();
    this.receivedEvents = new Map();
  }

  async connectUser(user) {
    return new Promise((resolve, reject) => {
      const token = createMockToken(user.clerkId);
      
      const socket = io(SOCKET_URL, {
        auth: { token },
        transports: ['websocket', 'polling']
      });

      socket.on('connect', () => {
        console.log(`✅ ${user.name} connected with socket ID: ${socket.id}`);
        this.connections.set(user.clerkId, socket);
        this.receivedEvents.set(user.clerkId, []);
        
        // Listen for all workflow-related events
        const events = [
          'workflowRunProgress',
          'nodeRunProgress', 
          'nodeExecution',
          'workflowCompleted',
          'decisionNodeResult'
        ];

        events.forEach(eventName => {
          socket.on(eventName, (data) => {
            console.log(`📨 ${user.name} received ${eventName}:`, data);
            this.receivedEvents.get(user.clerkId).push({
              event: eventName,
              data,
              timestamp: new Date()
            });
          });
        });

        resolve(socket);
      });

      socket.on('connect_error', (error) => {
        console.error(`❌ ${user.name} connection failed:`, error.message);
        reject(error);
      });

      socket.on('disconnect', (reason) => {
        console.log(`🔌 ${user.name} disconnected:`, reason);
      });
    });
  }

  async testUserSpecificEvents() {
    console.log('🧪 Starting user-specific socket events test...\n');

    try {
      // Connect both test users
      console.log('📡 Connecting test users...');
      await Promise.all(TEST_USERS.map(user => this.connectUser(user)));
      
      console.log('\n✅ All users connected successfully');
      console.log('📊 Current connections:', Array.from(this.connections.keys()));

      // Wait a moment for connections to stabilize
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Test 1: Verify each user only receives events for their own workflows
      console.log('\n🔍 Test 1: User-specific event filtering');
      console.log('Note: This test requires actual workflows owned by the test users');
      console.log('In a real scenario, you would trigger workflow events and verify isolation');

      // Test 2: Verify connection isolation
      console.log('\n🔍 Test 2: Connection isolation');
      const user1Socket = this.connections.get(TEST_USERS[0].clerkId);
      const user2Socket = this.connections.get(TEST_USERS[1].clerkId);

      if (user1Socket && user2Socket) {
        console.log(`User 1 socket ID: ${user1Socket.id}`);
        console.log(`User 2 socket ID: ${user2Socket.id}`);
        console.log('✅ Users have separate socket connections');
      }

      // Wait for any events
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Report results
      this.reportResults();

    } catch (error) {
      console.error('❌ Test failed:', error);
    } finally {
      this.cleanup();
    }
  }

  reportResults() {
    console.log('\n📊 Test Results:');
    console.log('================');

    for (const [userId, events] of this.receivedEvents.entries()) {
      const user = TEST_USERS.find(u => u.clerkId === userId);
      console.log(`\n${user.name} (${userId}):`);
      
      if (events.length === 0) {
        console.log('  No events received');
      } else {
        events.forEach((event, index) => {
          console.log(`  ${index + 1}. ${event.event} at ${event.timestamp.toISOString()}`);
        });
      }
    }

    console.log('\n✅ Test completed successfully');
    console.log('💡 To fully test user-specific events, create workflows owned by different users and trigger them');
  }

  cleanup() {
    console.log('\n🧹 Cleaning up connections...');
    for (const [userId, socket] of this.connections.entries()) {
      socket.disconnect();
      console.log(`🔌 Disconnected ${userId}`);
    }
    this.connections.clear();
    this.receivedEvents.clear();
  }
}

// Run the test
if (require.main === module) {
  const tester = new SocketTester();
  tester.testUserSpecificEvents()
    .then(() => {
      console.log('\n🎉 All tests completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test suite failed:', error);
      process.exit(1);
    });
}

module.exports = SocketTester;
