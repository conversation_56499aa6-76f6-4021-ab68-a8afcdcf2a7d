const prisma = require("@/config/db");

const authMiddleware = async (req, res, next) => {
  try {
    // Get auth object
    const auth = req.auth;
    if (process.env.NODE_ENV === "development") {
      console.log("Auth Middleware Debug: ", auth);
    }

    // Check if user is authenticated
    if (!auth || !auth.userId) {
      console.log("Authentication failed - no valid session");
      return res.status(401).json({ error: "Unauthorized" });
    }

    const clerkUserId = auth.userId;
    const companyId = auth.sessionClaims?.public_metadata?.companyId;
    const userId = auth.sessionClaims?.public_metadata?.userId;

    console.log("Auth values:", { clerkUserId, companyId, userId });

    if (!companyId || !userId) {
      return res.status(403).json({ 
        error: "Forbidden: Missing required metadata in session" 
      });
    }

    // Validate user exists and belongs to company
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { company: true },
    });

    if (!user) {
      return res.status(403).json({ error: "Forbidden: User not found" });
    }

    if (user.companyId !== companyId) {
      return res.status(403).json({ 
        error: "Forbidden: User does not belong to company" 
      });
    }

    req.user = { userId, clerkUserId, companyId };
    next();
  } catch (error) {
    console.error("Auth middleware error:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};


module.exports = { authMiddleware };