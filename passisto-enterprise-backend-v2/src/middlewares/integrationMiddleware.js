const { JiraIntegrationSchema } = require("./schema/jira.schema");
const { FtpIntegrationSchema } = require("./schema/ftp.schema");
const { WebIntegrationSchema } = require("./schema/web.schema");

const prisma = require("@/config/db");
const { getIndexSize } = require('@/utils/helpers')


const SCHEMAS = {
  jira: JiraIntegrationSchema,
  ftp: FtpIntegrationSchema,
  web: WebIntegrationSchema,
};

async function validateIntegration(req, res, next){
  const { providerType } = req.body;

  if (!providerType || !(providerType in SCHEMAS)) {
    return res.status(400).json({ error: "Invalid or missing providerType" });
  }

  const schema = SCHEMAS[providerType];

  try {
    req.body = schema.parse(req.body);
    next();
  } catch (err) {
    return res.status(400).json({ error: err.errors });
  }
};


async function verifyStorageQuota(req, res, next) {
  try {
    const { companyId } = req.user;

    if (!companyId) {
      return res.status(400).json({ error: "Company ID is missing" });
    }

    // Step 1: Fetch all OpenSearch indices belonging to the company
    const indices = await prisma.integration.findMany({
      where: { companyId },
      select: { opensearchIndexId: true },
    });

    let totalSizeInGb = 0;
    
    if (indices.length) {
      const indexIds = indices.map((index) => index.opensearchIndexId);
      // Step 2: Fetch sizes of these indices from OpenSearch
      const indexSizes = await Promise.all(
        indexIds.map(async (indexId) => await getIndexSize(indexId)) // Await each promise
      );
      totalSizeInGb = indexSizes.reduce((sum, size) => sum + size, 0); // Sum up the sizes
    }

    // Step 3: Fetch current usage from CompanyUsage
    const currentUsage = await prisma.companyUsage.findUnique({
      where: { companyId_key: { companyId, key: "storageGbUsed" } },
    });

    // Step 4: Update the CompanyUsage table
    if (!currentUsage || currentUsage.value !== totalSizeInGb)
    {
        await prisma.companyUsage.upsert({
        where: { companyId_key: { companyId, key: "storageGbUsed" } },
        update: { value: totalSizeInGb },
        create: { companyId, key: "storageGbUsed", value: totalSizeInGb },
      });
    }

    // Step 5: Fetch the storage limit from the subscription plan
    const subscriptionFeature = await prisma.subscriptionFeature.findFirst({
      where: {
        subscription: { companies: { some: { id: companyId } } },
        key: "storageLimitGb",
      },
    });

    if (!subscriptionFeature) {
      return res.status(400).json({ error: "Storage limit not defined for the subscription" });
    }

    const storageLimitGb = subscriptionFeature.value;
    totalSizeInGb = currentUsage.value
    // Step 6: Verify if the usage exceeds the limit
    if (totalSizeInGb >= storageLimitGb) {
      return res.status(402).json({
        error: "Storage quota exceeded. Cannot create a new integration.",
        limit: storageLimitGb
      });
    }

    // Proceed to the next middleware
    next();
  } catch (error) {
    console.error("Error in verifyStorageQuota middleware:", error);
    res.status(500).json({ error: "Internal server error" });
  }
}

module.exports = { validateIntegration, verifyStorageQuota };