// const console = require('./console');
const jwt = require('jsonwebtoken');
const { PrismaClient } = require('@prisma/client');

function setupSocketHandler(io, workflowExecutionService) {
  // Store user-socket mappings
  const userSockets = new Map(); // userId -> Set of socket IDs
  const socketUsers = new Map(); // socket ID -> user info

  // Middleware to handle authentication and connection setup
  io.use(async (socket, next) => {
    console.info("New socket connection attempt", { socketId: socket.id });

    try {
      // Get authentication token from handshake
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');

      if (!token) {
        console.warn("Socket connection rejected: No authentication token", { socketId: socket.id });
        return next(new Error('Authentication token required'));
      }

      // Verify the token (assuming it's a Clerk JWT)
      // In production, you should verify with Clerk's public key
      const decoded = jwt.decode(token);

      if (!decoded || !decoded.sub) {
        console.warn("Socket connection rejected: Invalid token", { socketId: socket.id });
        return next(new Error('Invalid authentication token'));
      }

      const clerkUserId = decoded.sub;

      // Get user from database
      const prisma = new PrismaClient();
      const user = await prisma.user.findUnique({
        where: { clerkId: clerkUserId },
        select: {
          id: true,
          clerkId: true,
          firstName: true,
          lastName: true,
          email: true,
          companyId: true
        }
      });

      if (!user) {
        console.warn("Socket connection rejected: User not found", { socketId: socket.id, clerkUserId });
        return next(new Error('User not found'));
      }

      // Store user info with socket
      socket.userId = user.id;
      socket.userInfo = user;

      console.info("Socket authenticated successfully", {
        socketId: socket.id,
        userId: user.id,
        userEmail: user.email
      });

      next();
    } catch (error) {
      console.error("Socket authentication error", {
        socketId: socket.id,
        error: error.message
      });
      next(new Error('Authentication failed'));
    }
  });

  io.on("connection", (socket) => {
    const userId = socket.userId;
    const userInfo = socket.userInfo;

    console.info("Client connected", {
      socketId: socket.id,
      userId: userId,
      userEmail: userInfo.email,
      transport: socket.conn.transport.name
    });

    // Add socket to user mapping
    if (!userSockets.has(userId)) {
      userSockets.set(userId, new Set());
    }
    userSockets.get(userId).add(socket.id);
    socketUsers.set(socket.id, userInfo);

    // Handle client errors
    socket.on("error", (error) => {
      console.error(`Socket error`, { socketId: socket.id, error: error.message, stack: error.stack });
    });

    socket.on("disconnect", async (reason) => {
      console.info("Client disconnected", {
        socketId: socket.id,
        userId: userId,
        reason
      });

      // Clean up user-socket mappings
      if (userSockets.has(userId)) {
        userSockets.get(userId).delete(socket.id);
        if (userSockets.get(userId).size === 0) {
          userSockets.delete(userId);
        }
      }
      socketUsers.delete(socket.id);
    });

    // Handle workflow run start
    socket.on("workflowRun:start", async ({ workflowId }) => {
      console.info("Starting workflow", { workflowId, socketId: socket.id, userId: userId });

      try {
        const { PrismaClient } = require('@prisma/client');
        const prisma = new PrismaClient();

        // Check if workflow exists and belongs to the user
        const workflow = await prisma.workflow.findUnique({
          where: { id: workflowId },
          include: { user: true }
        });

        if (!workflow) {
          throw new Error('Workflow not found');
        }

        // Check if the user owns the workflow
        if (workflow.userId !== userId) {
          throw new Error('Unauthorized: You do not own this workflow');
        }

        // Create a new workflow run
        const workflowRun = await prisma.workflowRun.create({
          data: {
            workflowId,
            status: 'PENDING'
          }
        });

        // Start workflow execution
        await workflowExecutionService.startWorkflowRun(workflowRun.id);

        // Send the workflowRun ID back to the client
        socket.emit("workflowRun:created", {
          workflowRunId: workflowRun.id,
          workflowId: workflowId
        });
      } catch (error) {
        console.error("Error starting workflow", { workflowId, socketId: socket.id, userId: userId, error: error.message, stack: error.stack });
        socket.emit("workflowRun:error", { message: error.message });
      }
    });

    // Handle workflow run stop
    socket.on("workflowRun:stop", async ({ workflowRunId }) => {
      console.info("Stopping workflow run", { workflowRunId, socketId: socket.id });
      try {
        await workflowExecutionService.stopWorkflowRun(workflowRunId);
      } catch (error) {
        console.error("Error stopping workflow run", { workflowRunId, socketId: socket.id, error: error.message, stack: error.stack });
        socket.emit("workflowRun:error", { message: error.message });
      }
    });

    // Handle user task completion
    socket.on("userTask:complete", async ({ workflowRunId, nodeId, userId, taskData }) => {
      console.info("User completing task", { userId, workflowRunId, nodeId, socketId: socket.id });
      try {
        // Validate required parameters
        if (!workflowRunId || !nodeId || !userId) {
          throw new Error('Missing required parameters');
        }

        // Complete the user task
        await workflowExecutionService.completeUserTask(workflowRunId, nodeId, userId, taskData || {});

        // Notify the client that the task was completed
        socket.emit("userTask:completed", {
          workflowRunId,
          nodeId,
          success: true,
          message: 'Task completed successfully'
        });
      } catch (error) {
        console.error("Error completing user task", { userId, workflowRunId, nodeId, socketId: socket.id, error: error.message, stack: error.stack });
        socket.emit("userTask:error", {
          workflowRunId,
          nodeId,
          message: error.message
        });
      }
    });
  });

  // Helper function to emit events to specific users
  const emitToUser = (userId, event, data) => {
    if (userSockets.has(userId)) {
      const socketIds = userSockets.get(userId);
      socketIds.forEach(socketId => {
        const socket = io.sockets.sockets.get(socketId);
        if (socket) {
          socket.emit(event, data);
        }
      });
      console.debug(`Emitted ${event} to user ${userId} (${socketIds.size} sockets)`);
    } else {
      console.debug(`User ${userId} not connected, skipping ${event} emission`);
    }
  };

  // Helper function to emit events to workflow owner
  const emitToWorkflowOwner = async (workflowId, event, data) => {
    try {
      const prisma = new PrismaClient();
      const workflow = await prisma.workflow.findUnique({
        where: { id: workflowId },
        select: { userId: true }
      });

      if (workflow) {
        emitToUser(workflow.userId, event, data);
      } else {
        console.warn(`Workflow ${workflowId} not found for event ${event}`);
      }
    } catch (error) {
      console.error(`Error emitting ${event} to workflow owner:`, error);
    }
  };

  // Helper function to emit events to workflow run owner
  const emitToWorkflowRunOwner = async (workflowRunId, event, data) => {
    try {
      const prisma = new PrismaClient();
      const workflowRun = await prisma.workflowRun.findUnique({
        where: { id: workflowRunId },
        include: { workflow: { select: { userId: true } } }
      });

      if (workflowRun) {
        emitToUser(workflowRun.workflow.userId, event, data);
      } else {
        console.warn(`WorkflowRun ${workflowRunId} not found for event ${event}`);
      }
    } catch (error) {
      console.error(`Error emitting ${event} to workflow run owner:`, error);
    }
  };

  // Attach helper functions to io for use by other services
  io.emitToUser = emitToUser;
  io.emitToWorkflowOwner = emitToWorkflowOwner;
  io.emitToWorkflowRunOwner = emitToWorkflowRunOwner;

  return io;
}

module.exports = setupSocketHandler;