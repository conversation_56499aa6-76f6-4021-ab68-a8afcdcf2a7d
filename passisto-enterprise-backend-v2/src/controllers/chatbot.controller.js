const createChatbotGraph = require("@/utils/chatbot-utils/chatbot-flow");
const {
  getMongoCollections,
  getMongoSessionHistory } = require("@/utils/chatbot-utils/chatbot-session");
const { AzureChatOpenAI } = require('@langchain/openai');
const prisma = require("../config/db");
const { v4: uuidv4 } = require('uuid');


  // Send a message to the chatbot
const sendMessage = async (req, res) => {
    const { userId } = req.user;
    // const { message, sessionId, indices } = req.body;
    const {
      message: message = "",
      indices = [],
      session_id: initialSessionId = "",
    } = req.body;

    console.log("Processing request with LangGraph:", req.body);
    
    const sessionId = initialSessionId || uuidv4();
    
    try {
      console.log("Processing request with LangGraph:", req.body);
      // Initialize LLM
      const llm = new AzureChatOpenAI({
        model: "gpt-4o-mini",
        temperature: 0,
        max_tokens: 200,
        azureOpenAIApiKey: process.env.AZURE_OPENAI_API_KEY,
        azureOpenAIApiInstanceName: process.env.AZURE_OPENAI_API_INSTANCE_NAME,
        azureOpenAIApiDeploymentName: process.env.AZURE_OPENAI_API_DEPLOYMENT_NAME,
        azureOpenAIApiVersion: process.env.AZURE_OPENAI_API_VERSION,
        user: sessionId,
        callbacks: [
          {
            handleLLMStart: async (_, prompts) => {
              console.log("\n>>>> LLM START ====");
              console.log(prompts[0]);
              console.log("<<<< LLM START ====\n");
            },
            handleLLMEnd: async (output) => {
              console.log("\n>>>> LLM END ====");
              console.log("Response:", output.generations[0][0].text);
              console.log("Tokens:", output.llmOutput?.tokenUsage?.totalTokens);
              console.log("<<<< LLM END ====\n");
            },
            handleLLMError: async (err) => {
              console.error("LLM Error:", err);
            },
          },
        ],
      });

      // Get session history
      const [, historyCollection] = await getMongoCollections();
      const sessionHistory = await getMongoSessionHistory(
        historyCollection,
        sessionId,
        userId
      );
      // Get existing messages for context
      const existingMessages = await sessionHistory.getMessages();
      // Create initial state
      const initialState = {
        question: message,
        messages: existingMessages,
        sessionId: sessionId,
        userId: userId,
        context: [],
        rephrased_question: "",
        answer: "",
        sources: [],
        error: null
      };
      // Create and run the graph
      const chatbotGraph = createChatbotGraph();
      const config = {
        configurable: {
          llm: llm,
          alias: indices,
          sessionHistory: sessionHistory
        }
      };
      // Execute the graph
      console.log("--- STARTING GRAPH EXECUTION ---");
      const finalState = await chatbotGraph.invoke(initialState, config);
      console.log("--- GRAPH EXECUTION COMPLETED ---");

      // Update user ID in MongoDB if provided
      if (userId) {
        try {
          await historyCollection.updateOne(
            { sessionId: sessionId },
            { $set: { userId: userId } },
            { upsert: true }
          );
          console.log(`Updated userId ${userId} for session ${sessionId}`);
        } catch (error) {
          console.error('Error updating userId:', error);
        }
      }

      // Update chat message usage after successful processing
      try {
        const { companyId } = req.user;
        await prisma.companyUsage.upsert({
          where: {
            companyId_key: {
              companyId,
              key: "chatMessagesUsed"
            }
          },
          update: {
            value: {
              increment: 1
            }
          },
          create: {
            companyId,
            key: "chatMessagesUsed",
            value: 1
          }
        });
        console.log(`Updated chat message usage for company ${companyId}`);
      } catch (usageError) {
        console.error('Error updating chat message usage:', usageError);
        // Don't fail the request if usage tracking fails
      }

      // Send response
      res.send({
        answer: finalState.answer,
        sources: finalState.sources,
        sessionId: sessionId,
        userId: userId || null,
        graph_state: {
          rephrased_question: finalState.rephrased_question,
          context_count: finalState.context.length,
          error: finalState.error
        }
      });
    
    }catch (error) {
      console.error('LangGraph chatbot error:', error);
      res.status(500).send({
        answer: "Something went wrong, please try again later.",
        sessionId: sessionId,
        userId: userId || null,
        error: error.message
      });
   }
};


// Get chat history for a specific session
const getChatHistory = async (req, res) => {
  const { session_id: sessionId } = req.params;
  const { userId } = req.user;
  try {
    const [, historyCollection] = await getMongoCollections(); // Use empty first element to ignore mongoClient
    // Query the MongoDB collection directly to get history with userId filter
    let query = { sessionId: sessionId };
    // If userId is provided, check only in the document root
    if (userId) {
      query = {
        $and: [
          { userId: userId },
          { sessionId: sessionId }
        ]
      };
    }

    const historyData = await historyCollection.findOne(query);
    if (!historyData) {
      return res.status(404).send({
        message: "History not found",
      });
    }
    res.send({
      history: historyData,
      userId: userId || null,
    });
  } catch (error) {
    console.error('History retrieval error:', error);
    res.status(500).send({
      message: "Something went wrong, please try again later.",
    });
  }
};


// Get all chat sessions for a user
const getUserSessions = async (req, res) => {
  const { userId } = req.user;
  const limit = parseInt(req.query.limit) || 10;
  const skip = parseInt(req.query.skip) || 0;

  try {
    // Get MongoDB collections and ensure they're properly initialized
    const [, historyCollection] = await getMongoCollections(); // Use empty first element to ignore mongoClient
    if (!historyCollection) {
      console.error('History collection is undefined');
      return res.status(500).send({
        message: "Database connection error. Please try again later.",
      });
    }
    // Query MongoDB for all sessions with this user ID
    // Only check userId in the document root
    const query = { userId: userId };
    // Get total count for pagination
    const total = await historyCollection.countDocuments(query);
    if (total === 0) {
      return res.status(404).send({
        message: "No sessions found for this user",
        sessions: [],
        total: 0
      });
    }
    // Find all sessions and sort by most recent first
    const sessions = await historyCollection
      .find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();
    // Process the sessions to extract relevant information
    const processedSessions = sessions.map(session => {
      // Get the last message if available
      const messages = session.messages || [];

      // Extract the last message content
      let lastMessage = "";
      if (messages.length > 0) {
        const lastMsg = messages[messages.length - 1];
        // Handle different message structures
        if (lastMsg?.data?.content) {
          lastMessage = lastMsg.data.content;
        } else if (lastMsg?.content) {
          lastMessage = lastMsg.content;
        } else if (typeof lastMsg === 'string') {
          lastMessage = lastMsg;
        }
      }
      // Extract creation date from ObjectId if not available directly
      const createdAt = session.createdAt ||
                        (session._id ? new Date(parseInt(session._id.toString().substring(0, 8), 16) * 1000) : new Date());

      return {
        session_id: session.sessionId,
        created_at: createdAt,
        last_message: lastMessage,
        message_count: messages.length,
      };
    });

    res.send({
      sessions: processedSessions,
      total,
      user_id: userId
    });
  } catch (error) {
    console.error('User sessions retrieval error:', error);
    res.status(500).send({
      message: "Something went wrong, please try again later.",
    });
  }
};


// Delete a chat session
const deleteSession = async (req, res) => {
  const { session_id: sessionId } = req.params;
  const { userId } = req.user;

  // For security, require a userId to delete a session
  if (!userId) {
    return res.status(400).send({
      message: "userId is required to delete a session",
      sessionId: sessionId
    });
  }
  try {
    const [, historyCollection] = await getMongoCollections();
    // First check if the session exists and belongs to the user
    const session = await historyCollection.findOne({
      sessionId: sessionId,
      userId: userId
    });
    if (!session) {
      // Check if the session exists but doesn't belong to this user
      const sessionExists = await historyCollection.findOne({ sessionId: sessionId });
      if (sessionExists) {
        return res.status(403).send({
          message: "You don't have permission to delete this session",
          sessionId: sessionId
        });
      } else {
        return res.status(404).send({
          message: "Session not found",
          sessionId: sessionId
        });
      }
    }
    // Delete the session
    const result = await historyCollection.deleteOne({
      sessionId: sessionId,
      userId: userId
    });
    if (result.deletedCount === 1) {
      return res.status(200).send({
        message: "Session deleted successfully",
        sessionId: sessionId
      });
    } else {
      return res.status(500).send({
        message: "Failed to delete session",
        sessionId: sessionId
      });
    }
  } catch (error) {
    console.error('Session deletion error:', error);
    res.status(500).send({
      message: "Something went wrong, please try again later.",
      sessionId: sessionId
    });
  }
};

module.exports = {
  sendMessage,
  getChatHistory,
  getUserSessions,
  deleteSession,
};