require('module-alias/register');
require("dotenv").config();

const getVectorStore = require("@/config/vectorStore");
const { AzureChatOpenAI } = require('@langchain/openai');
const { EnsembleRetriever } = require("langchain/retrievers/ensemble");
const { BM25Retriever } = require("@langchain/community/retrievers/bm25");
const prisma = require("../config/db");

// Perform a search query
const performSearch = async (req, res) => {
// const performSearch = async (query, indices) => {
  const { userId } = req.user; // Extract user ID from the authenticated user
  const { query, indices } = req.body;
  
  try {
    console.log(`Search request received: ${query}`);
    console.log(`Using alias: ${indices}`);
    if (userId) console.log(`User ID: ${userId}`)

    // Initialize Azure OpenAI for query expansion
    const llm = new AzureChatOpenAI({
      azureOpenAIApiKey: process.env.AZURE_OPENAI_API_KEY,
      azureOpenAIApiVersion: process.env.AZURE_OPENAI_API_VERSION,
      azureOpenAIApiInstanceName: process.env.AZURE_OPENAI_API_INSTANCE_NAME,
      azureOpenAIApiDeploymentName: process.env.AZURE_OPENAI_API_DEPLOYMENT_NAME,
      temperature: 0,
    });
    // Get vector store
    const vectorStore = await getVectorStore(indices);

    // Create base retriever with similarity search
    const baseRetriever = vectorStore.asRetriever({
      searchType: "similarity",
      searchKwargs: {
        k: 15, // Increased to get more documents
      },
    });
     // Create a second retriever with a higher k value for more results
     const secondaryRetriever = vectorStore.asRetriever({
      searchType: "similarity",
      searchKwargs: {
        k: 10,
      },
    });

    // Use ensemble retriever with just similarity search retrievers
    // since OpenSearch doesn't support MMR
    const ensembleRetriever = new EnsembleRetriever({
      retrievers: [baseRetriever, secondaryRetriever],
      weights: [0.7, 0.3], // Adjust weights to prioritize different retrievers
    });

     // Perform query expansion
     const expandedQueryResponse = await llm.invoke([
      ["system", "You are a search query expansion assistant. Your task is to expand the user's query into multiple related queries that might help retrieve more relevant information. Return 3 expanded queries as a numbered list, each on a new line. Keep the original query intent intact."],
      ["human", `Original query: ${query}`]
    ]);

    // Extract expanded queries
    const expandedQueriesText = expandedQueryResponse.content;
    const expandedQueries = expandedQueriesText
      .split('\n')
      .filter(line => line.trim().match(/^\d+\.\s/))
      .map(line => line.replace(/^\d+\.\s/, '').trim());

    // Add original query to expanded queries
    const allQueries = [query, ...expandedQueries];
    console.log("Queries for search:", allQueries);

    // Retrieve documents for all queries
    const retrievalPromises = allQueries.map(q => ensembleRetriever.getRelevantDocuments(q));
    const retrievalResults = await Promise.all(retrievalPromises);

    // Flatten and deduplicate results
    const allDocuments = retrievalResults.flat();
    const uniqueDocuments = [];
    const seenSources = new Set();

    for (const doc of allDocuments) {
      // Create a unique identifier for the document based on content and source
      const docIdentifier = `${doc.metadata.source}-${doc.pageContent.substring(0, 50)}`;

      if (!seenSources.has(docIdentifier)) {
        seenSources.add(docIdentifier);
        uniqueDocuments.push(doc);
      }
    }

    console.log(`Retrieved ${uniqueDocuments.length} unique documents before BM25 reranking`);
    // Apply BM25 reranking to the unique documents
    const bm25Retriever = BM25Retriever.fromDocuments(uniqueDocuments, {
      k: Math.min(uniqueDocuments.length, 15), // Return top 15 documents after reranking or all if less than 15
    });

    console.log("BM25 retriever created successfully");
    
    // Variable to hold the final documents for response
    let finalDocs;
    try {
      // Sanitize the query to avoid regex issues in BM25
      // Replace special regex characters with spaces
      const sanitizedQuery = query.replace(/[\\^$.*+?()[\]{}|]/g, ' ').trim();
      // Use BM25 to rerank the documents with the sanitized query
      const rerankedDocs = await bm25Retriever.getRelevantDocuments(sanitizedQuery || query);
      console.log(`Retrieved ${rerankedDocs.length} documents after BM25 reranking`);
      finalDocs = rerankedDocs;
    
    } catch (error) {
      console.error('BM25 reranking error:', error);
      console.log('Falling back to original documents without BM25 reranking');
      // If BM25 fails, return the original documents (limited to top 15)
      finalDocs = uniqueDocuments.slice(0, 15);
    }

    // Format the response using the final documents
    const sources = finalDocs.map(doc => ({
      content: doc.pageContent,
      metadata: doc.metadata
    }));

    // console.log({
    //     query: query,
    //     expanded_queries: expandedQueries,
    //     sources: sources,
    //   })

    // Update search query usage after successful processing
    try {
      const { companyId } = req.user;
      await prisma.companyUsage.upsert({
        where: {
          companyId_key: {
            companyId,
            key: "searchQueriesUsed"
          }
        },
        update: {
          value: {
            increment: 1
          }
        },
        create: {
          companyId,
          key: "searchQueriesUsed",
          value: 1
        }
      });
      console.log(`Updated search query usage for company ${companyId}`);
    } catch (usageError) {
      console.error('Error updating search query usage:', usageError);
      // Don't fail the request if usage tracking fails
    }

    // Send response
    res.status(200).send({
      query: query,
      expanded_queries: expandedQueries,
      sources: sources,
    });

  } catch (error) {
    console.error('Search error:', error);
    res.status(500).send({
      message: "Something went wrong with the search, please try again later.",
      query: query,
      expanded_queries: [],
      sources: [],
    });
  }
}

// Run the seed function if this file is executed directly
if (require.main === module) {
  const args = process.argv.slice(2);
  const query = args[0] || ""; // First argument as query
  const indices = args[1] ? args[1].split(',') : []; // Second argument as comma-separated indices

  performSearch(query, indices)
    .then(() => {
      console.log("Search completed successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Search failed:", error);
      process.exit(1);
    });
}

module.exports = {
  performSearch,
};