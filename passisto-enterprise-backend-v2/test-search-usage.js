/**
 * Simple test script to verify search usage tracking implementation
 * This script tests the search usage tracking functionality by:
 * 1. Checking if the middleware is properly configured
 * 2. Testing the usage tracking after performing a search
 * 3. Verifying the usage limit checking
 */

const axios = require('axios');
const prisma = require('./src/config/db');

// Configuration
const BASE_URL = process.env.BACKEND_URL || 'http://localhost:5000';
const API_BASE = `${BASE_URL}/api/v1`;

// Test data
const testCompanyId = 'test-company-search-usage';
const testUserId = 'test-user-search-usage';
const testSubscriptionId = 'test-subscription-search-usage';

async function setupTestData() {
  console.log('Setting up test data...');
  
  try {
    // Clean up existing test data
    await prisma.companyUsage.deleteMany({
      where: { companyId: testCompanyId }
    });
    await prisma.user.deleteMany({
      where: { id: testUserId }
    });
    await prisma.company.deleteMany({
      where: { id: testCompanyId }
    });
    await prisma.subscriptionFeature.deleteMany({
      where: { subscriptionId: testSubscriptionId }
    });
    await prisma.subscription.deleteMany({
      where: { id: testSubscriptionId }
    });

    // Create test subscription with search query limit
    const subscription = await prisma.subscription.create({
      data: {
        id: testSubscriptionId,
        name: 'Test Subscription',
        stripeProductId: 'test-product-id',
        features: {
          create: [
            {
              key: 'maxSearchQueries',
              value: 3 // Set a low limit for testing
            }
          ]
        }
      }
    });

    // Create test company
    const company = await prisma.company.create({
      data: {
        id: testCompanyId,
        name: 'Test Company for Search Usage',
        subscriptionId: testSubscriptionId
      }
    });

    // Create test user
    const user = await prisma.user.create({
      data: {
        id: testUserId,
        clerkId: 'test-clerk-id-search',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        companyId: testCompanyId
      }
    });

    console.log('Test data setup completed successfully');
    return { company, user, subscription };
  } catch (error) {
    console.error('Error setting up test data:', error);
    throw error;
  }
}

async function testUsageTracking() {
  console.log('\n=== Testing Search Usage Tracking ===');
  
  try {
    // Test 1: Check initial usage (should be 0)
    console.log('\n1. Checking initial usage...');
    const initialUsage = await prisma.companyUsage.findUnique({
      where: {
        companyId_key: {
          companyId: testCompanyId,
          key: 'searchQueriesUsed'
        }
      }
    });
    
    console.log('Initial usage:', initialUsage?.value || 0);

    // Test 2: Check usage limit before performing searches
    console.log('\n2. Checking usage limit...');
    const usageCheckResponse = await axios.post(`${API_BASE}/company/check-usage-by-company-id`, {
      key: 'maxSearchQueries',
      companyId: testCompanyId
    });
    
    console.log('Usage limit check response:', {
      isExceeded: usageCheckResponse.data.isExceeded,
      remaining: usageCheckResponse.data.remaining,
      currentUsage: usageCheckResponse.data.currentUsage,
      limit: usageCheckResponse.data.limit
    });

    // Test 3: Simulate performing searches and check usage tracking
    console.log('\n3. Simulating search queries and usage tracking...');
    
    for (let i = 1; i <= 2; i++) {
      console.log(`\nPerforming search ${i}...`);
      
      // Manually update usage (simulating what the search controller does)
      await prisma.companyUsage.upsert({
        where: {
          companyId_key: {
            companyId: testCompanyId,
            key: 'searchQueriesUsed'
          }
        },
        update: {
          value: { increment: 1 }
        },
        create: {
          companyId: testCompanyId,
          key: 'searchQueriesUsed',
          value: 1
        }
      });

      // Check updated usage
      const updatedUsage = await prisma.companyUsage.findUnique({
        where: {
          companyId_key: {
            companyId: testCompanyId,
            key: 'searchQueriesUsed'
          }
        }
      });

      console.log(`Usage after search ${i}:`, updatedUsage.value);

      // Check if limit is exceeded
      const limitCheck = await axios.post(`${API_BASE}/company/check-usage-by-company-id`, {
        key: 'maxSearchQueries',
        companyId: testCompanyId
      });

      console.log(`Limit status after search ${i}:`, {
        isExceeded: limitCheck.data.isExceeded,
        remaining: limitCheck.data.remaining
      });
    }

    // Test 4: Test exceeding the limit
    console.log('\n4. Testing limit exceeded scenario...');
    
    // Perform more searches to exceed the limit
    for (let i = 3; i <= 4; i++) {
      await prisma.companyUsage.upsert({
        where: {
          companyId_key: {
            companyId: testCompanyId,
            key: 'searchQueriesUsed'
          }
        },
        update: {
          value: { increment: 1 }
        },
        create: {
          companyId: testCompanyId,
          key: 'searchQueriesUsed',
          value: 1
        }
      });
    }

    const finalLimitCheck = await axios.post(`${API_BASE}/company/check-usage-by-company-id`, {
      key: 'maxSearchQueries',
      companyId: testCompanyId
    });

    console.log('Final limit check (should be exceeded):', {
      isExceeded: finalLimitCheck.data.isExceeded,
      remaining: finalLimitCheck.data.remaining,
      currentUsage: finalLimitCheck.data.currentUsage,
      limit: finalLimitCheck.data.limit
    });

    console.log('\n✅ Search usage tracking test completed successfully!');
    
  } catch (error) {
    console.error('❌ Error during usage tracking test:', error.response?.data || error.message);
    throw error;
  }
}

async function cleanupTestData() {
  console.log('\nCleaning up test data...');
  
  try {
    await prisma.companyUsage.deleteMany({
      where: { companyId: testCompanyId }
    });
    await prisma.user.deleteMany({
      where: { id: testUserId }
    });
    await prisma.company.deleteMany({
      where: { id: testCompanyId }
    });
    await prisma.subscriptionFeature.deleteMany({
      where: { subscriptionId: testSubscriptionId }
    });
    await prisma.subscription.deleteMany({
      where: { id: testSubscriptionId }
    });
    
    console.log('Test data cleanup completed');
  } catch (error) {
    console.error('Error during cleanup:', error);
  }
}

async function runTest() {
  try {
    await setupTestData();
    await testUsageTracking();
  } catch (error) {
    console.error('Test failed:', error);
    process.exit(1);
  } finally {
    await cleanupTestData();
    await prisma.$disconnect();
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  runTest();
}

module.exports = { runTest, setupTestData, testUsageTracking, cleanupTestData };
