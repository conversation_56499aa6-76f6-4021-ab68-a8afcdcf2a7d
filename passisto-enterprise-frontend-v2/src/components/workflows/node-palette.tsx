"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  Play,
  Square,
  FileText,
  Mail,
  GitBranch,
  Clock,
  Database,
  Workflow,
  GripVertical,
  Bot,
  Mic,
  FileSearch,
} from "lucide-react"

type NodeType = {
  type: string
  label: string
  icon: React.ReactNode
  category: string
  description: string
}

const nodeTypes: NodeType[] = [
  // Flow control nodes
  // {
  //   type: "start",
  //   label: "Start",
  //   icon: <Play className="h-5 w-5 text-green-500" />,
  //   category: "flow",
  //   description: "Starting point of the workflow",
  // },
  {
    type: "end",
    label: "End",
    icon: <Square className="h-5 w-5 text-red-500" />,
    category: "flow",
    description: "Ending point of the workflow",
  },
  {
    type: "decision",
    label: "Decision",
    icon: <GitBranch className="h-5 w-5 text-yellow-500" />,
    category: "flow",
    description: "A decision point with multiple outcomes",
  },

  // AI nodes
  {
    type: "ask-ai",
    label: "Ask AI",
    icon: <Bot className="h-5 w-5 text-violet-500" />,
    category: "ai",
    description: "Generate text using AI models",
  },
  {
    type: "speech-to-text",
    label: "Speech to Text",
    icon: <Mic className="h-5 w-5 text-pink-500" />,
    category: "ai",
    description: "Convert audio to text",
  },
  // {
  //   type: "data-extraction",
  //   label: "Data Extraction",
  //   icon: <FileSearch className="h-5 w-5 text-cyan-500" />,
  //   category: "ai",
  //   description: "Extract structured data from text",
  // },

  // Action nodes
  {
    type: "task",
    label: "Task",
    icon: <FileText className="h-5 w-5 text-blue-500" />,
    category: "actions",
    description: "A task to be completed",
  },
  {
    type: "email",
    label: "Email",
    icon: <Mail className="h-5 w-5 text-purple-500" />,
    category: "actions",
    description: "Send an email notification",
  },
  // {
  //   type: "timer",
  //   label: "Timer",
  //   icon: <Clock className="h-5 w-5 text-orange-500" />,
  //   category: "actions",
  //   description: "Wait for a specified duration",
  // },
  // {
  //   type: "database",
  //   label: "Database",
  //   icon: <Database className="h-5 w-5 text-indigo-500" />,
  //   category: "actions",
  //   description: "Interact with a database",
  // },
  // {
  //   type: "subprocess",
  //   label: "Subprocess",
  //   icon: <Workflow className="h-5 w-5 text-teal-500" />,
  //   category: "actions",
  //   description: "Run a nested workflow",
  // },
]

export function NodePalette() {
  const [width, setWidth] = useState(260) // Default width
  const [isDragging, setIsDragging] = useState(false)
  const minWidth = 200
  const maxWidth = 400

  // Start dragging
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  // Handle dragging
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        const newWidth = e.clientX
        if (newWidth >= minWidth && newWidth <= maxWidth) {
          setWidth(newWidth)
        }
      }
    }

    const handleMouseUp = () => {
      setIsDragging(false)
    }

    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove)
      document.addEventListener("mouseup", handleMouseUp)
    }

    return () => {
      document.removeEventListener("mousemove", handleMouseMove)
      document.removeEventListener("mouseup", handleMouseUp)
    }
  }, [isDragging])

  const onDragStart = (event: React.DragEvent, nodeType: NodeType) => {
    event.dataTransfer.setData("application/reactflow/type", nodeType.type)
    event.dataTransfer.setData("application/reactflow/label", nodeType.label)
    event.dataTransfer.effectAllowed = "move"
  }

  return (
    <div style={{ width: `${width}px` }} className="relative h-full flex">
      <Card className="w-full h-full overflow-auto border-r">
        <CardHeader className="py-2 px-3">
          <CardTitle className="text-sm">Node Palette</CardTitle>
          <CardDescription className="text-xs">Drag nodes onto the canvas</CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <Tabs defaultValue="all">
            <TabsList className="w-full justify-start px-2 h-8">
              <TabsTrigger value="all" className="text-xs py-0 h-6">All</TabsTrigger>
              <TabsTrigger value="flow" className="text-xs py-0 h-6">Flow</TabsTrigger>
              <TabsTrigger value="ai" className="text-xs py-0 h-6">AI</TabsTrigger>
              <TabsTrigger value="actions" className="text-xs py-0 h-6">Actions</TabsTrigger>
            </TabsList>
            <TabsContent value="all" className="m-0">
              <div className="grid gap-1 p-2">
                {nodeTypes.map((nodeType) => (
                  <NodeItem key={nodeType.type} nodeType={nodeType} onDragStart={onDragStart} />
                ))}
              </div>
            </TabsContent>
            <TabsContent value="flow" className="m-0">
              <div className="grid gap-1 p-2">
                {nodeTypes
                  .filter((node) => node.category === "flow")
                  .map((nodeType) => (
                    <NodeItem key={nodeType.type} nodeType={nodeType} onDragStart={onDragStart} />
                  ))}
              </div>
            </TabsContent>
            <TabsContent value="ai" className="m-0">
              <div className="grid gap-1 p-2">
                {nodeTypes
                  .filter((node) => node.category === "ai")
                  .map((nodeType) => (
                    <NodeItem key={nodeType.type} nodeType={nodeType} onDragStart={onDragStart} />
                  ))}
              </div>
            </TabsContent>
            <TabsContent value="actions" className="m-0">
              <div className="grid gap-1 p-2">
                {nodeTypes
                  .filter((node) => node.category === "actions")
                  .map((nodeType) => (
                    <NodeItem key={nodeType.type} nodeType={nodeType} onDragStart={onDragStart} />
                  ))}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
      {/* Resize handle */}
      <button
        className="absolute top-0 right-0 w-2 h-full cursor-ew-resize flex items-center justify-center hover:bg-gray-200 active:bg-gray-300 transition-colors border-0 bg-transparent p-0"
        onMouseDown={handleMouseDown}
        aria-label="Resize panel"
        tabIndex={0}
      >
        <div className="h-8 flex items-center">
          <GripVertical className="h-4 w-4 text-gray-400" />
        </div>
      </button>
    </div>
  )
}

function NodeItem({
  nodeType,
  onDragStart,
}: Readonly<{
  nodeType: NodeType
  onDragStart: (event: React.DragEvent, nodeType: NodeType) => void
}>) {
  return (
    <button
      type="button"
      className="flex items-center gap-2 p-2 border rounded-md cursor-move hover:bg-muted transition-colors w-full text-left bg-transparent"
      draggable
      onDragStart={(event) => onDragStart(event, nodeType)}
      aria-label={`Drag ${nodeType.label} node`}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          // Can't really simulate drag with keyboard, but could show a message
          alert(`To add a ${nodeType.label} node, drag it to the canvas`);
        }
      }}
    >
      <div className="flex-shrink-0">{nodeType.icon}</div>
      <div className="flex-1">
        <div className="font-medium text-xs">{nodeType.label}</div>
        <div className="text-xs text-muted-foreground">{nodeType.description}</div>
      </div>
    </button>
  )
}

