"use client";

import { Panel } from "@xyflow/react";
import { AlertCircle } from "lucide-react";
import { NodeResult } from "./types";
import { NodeResultsPanel } from "../node-results-panel";
import { type Edge } from "@xyflow/react";

interface WorkflowStatusPanelsProps {
  isRunning: boolean;
  currentNodeId: string | null;
  nodeResults: NodeResult[];
  selectedEdge: Edge | null;
}

export function WorkflowStatusPanels({
  isRunning,
  currentNodeId,
  nodeResults,
  selectedEdge,
}: WorkflowStatusPanelsProps) {
  return (
    <>
      {/* Node Results Panel */}
      <Panel position="bottom-left" className="w-full max-w-full">
        <NodeResultsPanel
          results={nodeResults}
          currentNodeId={currentNodeId}
          isRunning={isRunning}
        />
      </Panel>

      {/* Edge selection info */}
      {selectedEdge && !isRunning && (
        <Panel position="bottom-center" className="bg-white p-2 rounded-md shadow-md flex items-center">
          <span>
            Selected connection: <span className="font-medium">{selectedEdge.source}</span> →{" "}
            <span className="font-medium">{selectedEdge.target}</span>
          </span>
        </Panel>
      )}
    </>
  );
}
