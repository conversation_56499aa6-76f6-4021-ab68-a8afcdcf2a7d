"use client";

import { useEffect, useCallback } from "react";
import { type Node, type Edge } from "@xyflow/react";
import { toast } from "@/hooks/use-toast";
import socketService from "@/services/socket";
import { useAuth } from "@clerk/nextjs";
import { Workflow } from "../workflows-table";
import {
  NodeResult,
  WorkflowProgress,
  NodeExecutionDetails,
  NodeRunProgress
} from "./types";

export function useWorkflowSocket(
  currentWorkflow: Workflow | null,
  nodes: Node[],
  edges: Edge[],
  setNodes: React.Dispatch<React.SetStateAction<Node[]>>,
  isRunning: boolean,
  setIsRunning: (isRunning: boolean) => void,
  setCurrentNodeId: (nodeId: string | null) => void,
  setExecutionPath: (pathOrUpdater: string[] | ((prev: string[]) => string[])) => void,
  setNodeResults: (resultsOrUpdater: NodeResult[] | ((prev: NodeResult[]) => NodeResult[])) => void,
  determineExecutionOrder: (nodes: Node[], edges: Edge[]) => string[]
) {
  // Handle workflow update from socket
  const handleWorkflowUpdate = useCallback((updatedWorkflow: Workflow & { isExecuting?: boolean; executionData?: any }) => {
    if (updatedWorkflow.id === currentWorkflow?.id) {
      console.log('Received real-time workflow update:', updatedWorkflow);

      // Update execution state based on the received data
      if (updatedWorkflow.isExecuting) {
        setIsRunning(true);

        if (updatedWorkflow.executionData?.currentNodeId) {
          setCurrentNodeId(updatedWorkflow.executionData.currentNodeId);

          // Update node styling
          setNodes((nodes) =>
            nodes.map((node) => ({
              ...node,
              data: {
                ...node.data,
                isActive: node.id === updatedWorkflow.executionData.currentNodeId,
                isCompleted: updatedWorkflow.executionData.executionPath?.includes(node.id) || false,
              },
            }))
          );
        }

        if (updatedWorkflow.executionData?.executionPath) {
          setExecutionPath(updatedWorkflow.executionData.executionPath);
        }
      } else {
        // Workflow is not running
        setIsRunning(false);
        setCurrentNodeId(null);
        setNodeResults([]);

        // Show a toast notification that the workflow has completed
        toast({
          title: "Workflow Status Updated",
          description: "The workflow is no longer running.",
        });

        // Reset node styling
        setNodes((nodes) =>
          nodes.map((node) => ({
            ...node,
            data: {
              ...node.data,
              isActive: false,
              isCompleted: false,
            },
          }))
        );
      }
    }
  }, [currentWorkflow?.id, setIsRunning, setCurrentNodeId, setNodes, setExecutionPath, setNodeResults]);

  // Handle workflow progress from socket
  const handleWorkflowProgress = useCallback((progress: WorkflowProgress) => {
    // Check if this event is for the current workflow
    // Only process events that explicitly match the current workflow ID
    const isForCurrentWorkflow = progress.workflowId && progress.workflowId === currentWorkflow?.id;

    if (isForCurrentWorkflow) {
      console.log('Received workflow progress update:', progress);

      // Update UI based on progress type
      if (progress.type === 'node_started' && progress.nodeId) {
        setCurrentNodeId(progress.nodeId);

        // Update node styling
        setNodes((nodes) =>
          nodes.map((node) => ({
            ...node,
            data: {
              ...node.data,
              isActive: node.id === progress.nodeId,
            },
          }))
        );
      } else if (progress.status === 'WAITING_FOR_USER' && progress.nodeId) {
        // Set current node ID
        setCurrentNodeId(progress.nodeId);

        // Update node styling for waiting task
        setNodes((nodes) =>
          nodes.map((node) => ({
            ...node,
            data: {
              ...node.data,
              isActive: node.id === progress.nodeId,
              isWaiting: node.id === progress.nodeId,
            },
          }))
        );

        // Show a toast notification
        toast({
          title: "Workflow Waiting for User",
          description: progress.message ?? "A task in the workflow is waiting for user input.",
          variant: "default",
        });
      } else if (progress.type === 'node_completed' && progress.nodeId) {
        // Add node to execution path
        setExecutionPath((prevPath) => {
          // Ensure prevPath is an array
          const path = Array.isArray(prevPath) ? prevPath : [];

          if (progress.nodeId && !path.includes(progress.nodeId)) {
            return [...path, progress.nodeId];
          }
          return path;
        });

        // Mark node as completed
        setNodes((nodes) =>
          nodes.map((node) => ({
            ...node,
            data: {
              ...node.data,
              isActive: false,
              isCompleted: node.id === progress.nodeId || node.data.isCompleted,
            },
          }))
        );
      } else if (progress.type === 'workflow_completed' || progress.type === 'workflow_failed' || progress.status === 'SUCCESS' || progress.status === 'FAILED') {
        console.log('Workflow execution completed with status:', progress.status ?? progress.type);
        console.log('WORKFLOW COMPLETION EVENT RECEIVED - Setting isRunning to false');
        console.log('Current isRunning state:', isRunning);

        // Reset execution state but stay on the page
        setIsRunning(false);
        setCurrentNodeId(null);

        // Reset all node states
        setNodes((nodes) =>
          nodes.map((node) => ({
            ...node,
            data: {
              ...node.data,
              isActive: false,
              isWaiting: false,  // Make sure to reset the waiting state
              // We can keep the completed status to show the execution path
              // isCompleted: false, // Uncomment this if you want to reset completed status too
            },
          }))
        );

        // Show a toast notification that the workflow has completed
        toast({
          title: (progress.type === 'workflow_completed' || progress.status === 'SUCCESS') ? "Workflow Completed" : "Workflow Failed",
          description: progress.message ?? ((progress.type === 'workflow_completed' || progress.status === 'SUCCESS') ? "The workflow has completed successfully." : "The workflow execution failed."),
          variant: (progress.type === 'workflow_completed' || progress.status === 'SUCCESS') ? "default" : "destructive",
        });
      }
    }
  }, [currentWorkflow?.id, isRunning, setIsRunning, setCurrentNodeId, setNodes, setExecutionPath]);

  // Handle node execution details from socket
  const handleNodeExecution = useCallback((executionDetails: NodeExecutionDetails) => {
    // Only process if we have a current workflow and the event is for a node in this workflow
    if (currentWorkflow && executionDetails.workflowRunId && executionDetails.nodeId && executionDetails.executionDetails) {
      // Check if the node belongs to the current workflow
      const nodeExists = nodes.find(n => n.id === executionDetails.nodeId);
      if (!nodeExists) {
        console.log("Ignoring node execution details for node not in current workflow:", executionDetails.nodeId);
        return;
      }

      console.log("Received node execution details:", executionDetails);

      const { nodeId, executionDetails: details } = executionDetails;
      const node = nodes.find(n => n.id === nodeId);

      // Determine the execution order of nodes
      const executionOrder = determineExecutionOrder(nodes, edges);

      // Update node results with execution details
      setNodeResults((prev: NodeResult[]) => {
        // Ensure prev is an array
        const prevResults = Array.isArray(prev) ? prev : [];

        const existingIndex = prevResults.findIndex((r: NodeResult) => r.nodeId === nodeId);
        const newResult: NodeResult = {
          nodeId,
          nodeType: details.nodeType ?? node?.type ?? 'unknown',
          nodeName: typeof node?.data?.label === 'string' ? node.data.label : String(nodeId),
          status: details.success ? 'SUCCESS' : 'FAILED',
          executionTime: details.executionTime,
          output: details.outputs,
          error: details.error ? details.error.message ?? details.error : null,
          order: executionOrder.indexOf(nodeId), // Add order property
        };

        console.log("Adding node result:", newResult);

        if (existingIndex >= 0) {
          const updated = [...prevResults];
          updated[existingIndex] = { ...updated[existingIndex], ...newResult };
          return updated;
        } else {
          return [...prevResults, newResult];
        }
      });
    }
  }, [nodes, edges, setNodeResults, determineExecutionOrder]);



  // Handle node run progress from socket
  const handleNodeRunProgress = useCallback((progress: NodeRunProgress) => {
    // Only process if we have a current workflow and the event is for a node in this workflow
    if (currentWorkflow && progress.workflowRunId && progress.nodeId) {
      // Check if the node belongs to the current workflow
      const nodeExists = nodes.find(n => n.id === progress.nodeId);
      if (!nodeExists) {
        console.log("Ignoring node run progress for node not in current workflow:", progress.nodeId);
        return;
      }

      console.log("Received node run progress:", progress);

      const { nodeId, status, output } = progress;
      const node = nodes.find(n => n.id === nodeId);

      // Determine the execution order of nodes
      const executionOrder = determineExecutionOrder(nodes, edges);

      // Always update the node's state based on status
      setNodes((nodes) =>
        nodes.map((n) => ({
          ...n,
          data: {
            ...n.data,
            isActive: n.id === nodeId && (status === 'RUNNING' || status === 'WAITING_FOR_USER'),
            // Only set isWaiting to true if the node is currently waiting for user input
            // For any other status, explicitly set it to false to prevent stuck states
            isWaiting: n.id === nodeId && status === 'WAITING_FOR_USER',
            isCompleted: n.id === nodeId && status === 'SUCCESS' ? true : n.data?.isCompleted || false,
          },
        }))
      );

      // Set current node ID for highlighting or reset it if the node is completed/failed
      if (status === 'RUNNING' || status === 'WAITING_FOR_USER') {
        setCurrentNodeId(nodeId);

        // If this is a task node waiting for user input, show a toast notification
        if (status === 'WAITING_FOR_USER') {
          toast({
            title: "Task Waiting for User",
            description: progress.message ?? `Task assigned to ${progress.assigneeName ?? 'a user'} is waiting for completion.`,
            variant: "default",
          });
        }
      } else if (status === 'SUCCESS' || status === 'FAILED' || status === 'SKIPPED') {
        // For completed, failed, or skipped nodes, always reset their active and waiting states
        // This ensures that task nodes don't keep glowing after completion
        setNodes((nodes) =>
          nodes.map((n) => {
            if (n.id === nodeId) {
              return {
                ...n,
                data: {
                  ...n.data,
                  isActive: false,
                  isWaiting: false,
                  // Keep the completed status if it succeeded
                  isCompleted: status === 'SUCCESS' ? true : n.data?.isCompleted || false,
                  // Add skipped status
                  isSkipped: status === 'SKIPPED' ? true : n.data?.isSkipped || false,
                },
              };
            }
            return n;
          })
        );

        // If this was the current active node, reset the current node ID
        const activeNodes = nodes.filter(n => n.data?.isActive);
        if (activeNodes.length === 1 && activeNodes[0].id === nodeId) {
          setCurrentNodeId(null);
        }

        // Show a toast notification for completed task
        if (status === 'SUCCESS' && node?.type === 'task') {
          toast({
            title: "Task Completed",
            description: "The user task has been completed successfully.",
            variant: "default",
          });
        }
      }

      // Update node results with progress for all status types
      setNodeResults((prev: NodeResult[]) => {
        // Ensure prev is an array
        const prevResults = Array.isArray(prev) ? prev : [];

        const existingIndex = prevResults.findIndex((r: NodeResult) => r.nodeId === nodeId);
        const newResult: NodeResult = {
          nodeId,
          nodeType: node?.type ?? 'unknown',
          nodeName: typeof node?.data?.label === 'string' ? node.data.label : String(nodeId),
          status: status as any, // Type assertion to handle string status
          executionTime: new Date().toISOString(),
          output: output,
          error: status === 'FAILED' ? progress.message : undefined,
          order: executionOrder.indexOf(nodeId), // Add order property
        };

        console.log("Adding node result from progress:", newResult);

        if (existingIndex >= 0) {
          const updated = [...prevResults];
          updated[existingIndex] = { ...updated[existingIndex], ...newResult };
          return updated;
        } else {
          return [...prevResults, newResult];
        }
      });
    }
  }, [nodes, edges, setNodes, setCurrentNodeId, setNodeResults, determineExecutionOrder]);

  // Handle decision node result from socket
  const handleDecisionNodeResult = useCallback((result: any) => {
    console.log("Received decision node result:", result);

    // Only process if we have a current workflow and the event is for a node in this workflow
    if (currentWorkflow && result.nodeId && result.path) {
      // Check if the node belongs to the current workflow
      const nodeExists = nodes.find(n => n.id === result.nodeId);
      if (!nodeExists) {
        console.log("Ignoring decision node result for node not in current workflow:", result.nodeId);
        return;
      }
      // Update the decision node with the path information
      setNodes((nodes) =>
        nodes.map((node) => {
          if (node.id === result.nodeId) {
            return {
              ...node,
              data: {
                ...node.data,
                output: {
                  ...(node.data.output || {}),
                  path: result.path
                }
              },
            };
          }
          return node;
        })
      );
    }
  }, [setNodes]);

  // Handle workflow completion specifically
  const handleWorkflowCompletion = useCallback(() => {
    // Reset all node states to ensure no nodes are stuck in 'waiting' state
    setNodes((nodes) =>
      nodes.map((node) => ({
        ...node,
        data: {
          ...node.data,
          isActive: false,
          isWaiting: false,
          // We can keep completed status if needed
          // isCompleted: false,
        },
      }))
    );
  }, [setNodes]);

  // Get authentication token
  const { getToken } = useAuth();

  // Set up socket connection and event listeners
  useEffect(() => {
    const connectWithAuth = async () => {
      try {
        // Get authentication token
        const token = await getToken();

        // Connect to socket with authentication
        socketService.connect(token);
      } catch (error) {
        console.error('Failed to get authentication token for socket connection:', error);
        // Connect without token as fallback
        socketService.connect();
      }
    };

    connectWithAuth();

    // Register socket event listeners
    socketService.onWorkflowUpdate(handleWorkflowUpdate);
    socketService.addListener('workflowProgress', handleWorkflowProgress);
    socketService.addListener('workflowRunProgress', handleWorkflowProgress);
    socketService.addListener('nodeExecution', handleNodeExecution);
    socketService.addListener('nodeRunProgress', handleNodeRunProgress);
    socketService.addListener('decisionNodeResult', handleDecisionNodeResult);

    // Add specific listener for workflow completion
    socketService.addListener('workflowCompleted', handleWorkflowCompletion);

    // Clean up socket listeners on unmount
    return () => {
      socketService.offWorkflowUpdate(handleWorkflowUpdate);
      socketService.removeListener('workflowProgress', handleWorkflowProgress);
      socketService.removeListener('workflowRunProgress', handleWorkflowProgress);
      socketService.removeListener('nodeExecution', handleNodeExecution);
      socketService.removeListener('nodeRunProgress', handleNodeRunProgress);
      socketService.removeListener('decisionNodeResult', handleDecisionNodeResult);
      socketService.removeListener('workflowCompleted', handleWorkflowCompletion);
    };
  }, [
    handleWorkflowUpdate,
    handleWorkflowProgress,
    handleNodeExecution,
    handleNodeRunProgress,
    handleDecisionNodeResult,
    handleWorkflowCompletion
  ]);
}
