// src/components/integrations/StorageProgressBar.tsx

import React, { useEffect, useState } from "react";
import axios from "axios";
import { Progress } from "@/components/ui/progress";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import axiosInstance from "@/config/axios";
import { CHECK_STORAGE_USAGE } from "@/utils/routes";
import { useAuth } from "@clerk/nextjs";
import { AlertTriangle, HardDrive, CheckCircle } from "lucide-react";
import { useTranslations } from "next-intl";

interface StorageUsage {
  currentUsage: number;
  maxAllowedUsage: number;
}

const getColorClasses = (percentage: number) => {
  if (percentage >= 90) {
    return {
      bg: "bg-gradient-to-r from-red-500 to-red-600",
      text: "text-red-700",
      border: "border-red-200",
      icon: "text-red-500",
      glow: "shadow-red-500/20"
    };
  }
  if (percentage >= 70) {
    return {
      bg: "bg-gradient-to-r from-yellow-500 to-orange-500",
      text: "text-yellow-700",
      border: "border-yellow-200",
      icon: "text-yellow-500",
      glow: "shadow-yellow-500/20"
    };
  }
  return {
    bg: "bg-gradient-to-r from-green-500 to-emerald-500",
    text: "text-green-700",
    border: "border-green-200",
    icon: "text-green-500",
    glow: "shadow-green-500/20"
  };
};

const getStatusIcon = (percentage: number) => {
  if (percentage >= 90) return <AlertTriangle className="w-5 h-5" />;
  if (percentage >= 70) return <AlertTriangle className="w-5 h-5" />;
  return <CheckCircle className="w-5 h-5" />;
};

const StorageProgressBar: React.FC = () => {
  const t = useTranslations();
  const [usage, setUsage] = useState<StorageUsage | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { getToken } = useAuth();

  const getStatusText = (percentage: number) => {
    if (percentage >= 90) return t('critical-usage');
    if (percentage >= 70) return t('high-usage');
    return t('normal-usage');
  };

  useEffect(() => {
    const fetchUsage = async () => {
      try {
        setLoading(true);

        // Get the token from Clerk
        const token = await getToken();
        const headers = {
          Authorization: `Bearer ${token}`,
        };

        // Make the API call with the Clerk header
        const response = await axiosInstance.get(CHECK_STORAGE_USAGE, { headers });
        setUsage(response.data);
        setError(null);
      } catch (err) {
        setError(t('failed-to-load-storage'));
      } finally {
        setLoading(false);
      }
    };

    fetchUsage();
  }, [getToken, t]);

  if (loading) {
    return (
      <Card className="w-full max-w-xl shadow-lg border-0 bg-gradient-to-br from-slate-50 to-slate-100">
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-slate-200 rounded-full"></div>
              <div className="h-6 bg-slate-200 rounded w-32"></div>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <div className="h-4 bg-slate-200 rounded w-20"></div>
                <div className="h-4 bg-slate-200 rounded w-20"></div>
              </div>
              <div className="h-6 bg-slate-200 rounded-full"></div>
              <div className="h-3 bg-slate-200 rounded w-16"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !usage) {
    return (
      <Card className="w-full max-w-xl shadow-lg border-red-200 bg-gradient-to-br from-red-50 to-red-100">
        <CardContent className="p-6">
          <div className="flex items-center space-x-3 text-red-700">
            <AlertTriangle className="w-6 h-6" />
            <p className="font-medium">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const percentage = (usage.currentUsage / usage.maxAllowedUsage) * 100;
  const colors = getColorClasses(percentage);
  const remainingUsage = usage.maxAllowedUsage - usage.currentUsage;

  return (
    <Card className={`w-full max-w-xl shadow-xl border-0 bg-gradient-to-br from-white to-slate-50 transition-all duration-500 hover:shadow-2xl ${colors.glow}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-full bg-slate-100 ${colors.icon}`}>
              <HardDrive className="w-5 h-5" />
            </div>
            <div>
              <CardTitle className="text-xl font-bold text-slate-800">
                {t('storage-usage-title')}
              </CardTitle>
              <p className="text-sm text-slate-500 mt-1">
                {t('storage-usage-description')}
              </p>
            </div>
          </div>
          <div className={`flex items-center space-x-2 px-3 py-1 rounded-full border ${colors.border} bg-white/50`}>
            <div className={colors.icon}>
              {getStatusIcon(percentage)}
            </div>
            <span className={`text-sm font-medium ${colors.text}`}>
              {getStatusText(percentage)}
            </span>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="flex justify-between items-center text-sm">
          <div className="flex flex-col">
            <span className="font-semibold text-slate-700">
              {Number(usage.currentUsage).toFixed(1)} {t('gb')}
            </span>
            <span className="text-slate-500">{t('used')}</span>
          </div>
          <div className="flex flex-col text-center">
            <span className="font-semibold text-slate-700">
              {Number(remainingUsage.toFixed(1))} {t('gb')}
            </span>
            <span className="text-slate-500">{t('available')}</span>
          </div>
          <div className="flex flex-col text-right">
            <span className="font-semibold text-slate-700">
              {Number(usage.maxAllowedUsage.toFixed(1))} {t('gb')}
            </span>
            <span className="text-slate-500">{t('total')}</span>
          </div>
        </div>

        <div className="space-y-2">
          <div className="relative">
            <div className="w-full bg-slate-200 rounded-full h-6 overflow-hidden shadow-inner">
              <div
                className={`h-full transition-all duration-1000 ease-out ${colors.bg} shadow-lg`}
                style={{ width: `${Math.min(percentage, 100)}%` }}
              >
                <div className="h-full w-full bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
              </div>
            </div>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-xs font-bold text-white drop-shadow-lg">
                {percentage.toFixed(1)}%
              </span>
            </div>
          </div>
          
          <div className="flex justify-between text-xs text-slate-500">
            <span>0%</span>
            <span>100%</span>
          </div>
        </div>

        {percentage >= 90 && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-4 h-4 text-red-500" />
              <p className="text-sm text-red-700 font-medium">
                {t('storage-critical-message')}
              </p>
            </div>
          </div>
        )}

        {percentage >= 70 && percentage < 90 && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-4 h-4 text-yellow-500" />
              <p className="text-sm text-yellow-700 font-medium">
                {t('storage-warning-message')}
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default StorageProgressBar;