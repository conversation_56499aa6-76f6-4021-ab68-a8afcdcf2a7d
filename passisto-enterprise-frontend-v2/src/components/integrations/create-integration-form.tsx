"use client";

import { useState, useEffect } from "react";

import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Loader2, Info, PlusCircle } from "lucide-react";
import axios from "axios";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../components/ui/form";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
// import { ToastContainer} from "react-toastify";
import axiosInstance from "@/config/axios";
import {
  FTP_INTEGRATION,
  JIRA_INTEGRATION,
  WEB_INTEGRATION,
} from "@/utils/routes";
import { useAuth } from "@clerk/nextjs";
import {
  checkPermissions,
  integrationPermissions,
} from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from "@/hooks/useBackendUser";
import { toast } from "sonner";
import { useTranslations } from "next-intl";
import { UsageLimitDialog } from "@/components/usage-limit-dialog"; // Import the dialog

export function CreateIntegrationForm() {
  const t = useTranslations();
  const [isDialogOpen, setIsDialogOpen] = useState(false); // State to control dialog visibility
  const [dialogType, setDialogType] = useState<"interview" | "email" | "storage">("email"); // Dialog type
  const [dialogLimit, setDialogLimit] = useState(0); // Dialog limit

  // Form schemas for different integration types
  const jiraSchema = z.object({
    name: z.string().min(2, { message: t('name-must-be-at-least-2-characters') }),
    project: z.string().min(1, { message: t('project-is-required') }),
    domain: z.string().min(1, { message: t('domain-name-is-required') }),
    email: z.string().email({ message: t('please-enter-a-valid-email-address') }),
    token: z.string().min(1, { message: t('api-token-is-required') }),
    updateTime: z.coerce
      .number()
      .min(1, { message: t('update-time-must-be-at-least-1-day') }),
  });
  
  const ftpSchema = z.object({
    name: z.string().min(2, { message: t('name-must-be-at-least-2-characters-0') }),
    server: z.string().min(1, { message: t('server-ip-is-required') }),
    port: z.coerce.number().min(1, { message: t('port-is-required') }),
    username: z.string().min(1, { message: t('username-is-required') }),
    password: z.string().min(1, { message: t('password-is-required') }),
    isSecure: z.boolean(),
    updateTime: z.coerce
      .number()
      .min(1, { message: t('update-time-must-be-at-least-1-day-0') }),
  });
  
  const webSchema = z.object({
    name: z.string().min(2, { message: t('name-must-be-at-least-2-characters') }),
    url: z.string().url({ message: t('please-enter-a-valid-url') }),
    updateTime: z.coerce
      .number()
      .min(1, { message: t('update-time-must-be-at-least-1-day') }),
  });
  const [activeTab, setActiveTab] = useState("jira");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const { getToken } = useAuth();
  const { backendUser, loading: backendUserLoading } = useBackendUser();
  // Create forms for each integration type
  const jiraForm = useForm<z.infer<typeof jiraSchema>>({
    resolver: zodResolver(jiraSchema),
    defaultValues: {
      name: "",
      project: "",
      domain: "",
      email: "",
      token: "",
      updateTime: 1,
    },
  });

  const ftpForm = useForm<z.infer<typeof ftpSchema>>({
    resolver: zodResolver(ftpSchema),
    defaultValues: {
      name: "",
      server: "",
      port: 21,
      username: "",
      password: "",
      isSecure: true,
      updateTime: 1,
    },
  });

  const webForm = useForm<z.infer<typeof webSchema>>({
    resolver: zodResolver(webSchema),
    defaultValues: {
      name: "",
      url: "",
      updateTime: 1,
    },
  });

  // Handle form submission based on active tab
  const onSubmit = async (data: any) => {
    setIsSubmitting(true);

    try {
      let apiUrl = "";
      let payload = {};

      const token = await getToken();
      const headers = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      // Prepare API URL and payload based on the active tab
      if (activeTab === "jira") {
        apiUrl = JIRA_INTEGRATION;
        payload = {
          ...data,
          providerType: "jira",
        };
      } else if (activeTab === "ftp") {
        apiUrl = FTP_INTEGRATION;
        payload = {
          ...data,
          providerType: "ftp",
        };
      } else if (activeTab === "web") {
        apiUrl = WEB_INTEGRATION;
        payload = {
          ...data,
          providerType: "web",
        };
      }
      // Make API call
      await axiosInstance.post(apiUrl, payload, headers);

      // Show success notification
      toast.success(`"${data.name}" integration created successfully!`);

      // Redirect after successful creation
      router.push("/enterprise/integrations");
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.status === 402) {
        // Show dialog for quota exceeded
        setDialogType("storage"); // Set the type of dialog (adjust as needed)
        const limit = error.response.data?.limit || 0; // Extract the limit from the response
        setDialogLimit(limit); // Set the dialog limit dynamically
        setIsDialogOpen(true);
      } else {
        // Show error notification
        const errorMessage =
          axios.isAxiosError(error) &&
          error.response?.status !== 500 &&
          error.response?.data?.error
            ? error.response.data.error
            : `Failed to update "${data.name}" integration. Please try again.`;
        toast.error(errorMessage);

        console.error("Integration creation failed:", error);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    // Only check permissions after backendUser has loaded
    if (!backendUserLoading && backendUser) {
      const permissions = backendUser?.permissions ?? [];

      // Check if user has at least one integration-related permission
      const hasAnyIntegrationPermission = checkPermissions(permissions, [
        integrationPermissions.canCreateAll,
      ]);

      // Redirect if user doesn't have any integration-related permissions
      if (!hasAnyIntegrationPermission) {
        toast.error(t('permission-denied'), {
          description:
            t('you-dont-have-permission-to-access-the-integrations-page'),
        });
        router.back();
      }
    }
  }, [backendUser, backendUserLoading, router]);

  return (
    <>
      <div className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            {integrationPermissions.canCreateJira(
              backendUser?.permissions || []
            ) && <TabsTrigger value="jira">{t('jira-integration')}</TabsTrigger>}
            {integrationPermissions.canCreateFTP(
              backendUser?.permissions || []
            ) && <TabsTrigger value="ftp">{t('ftp-integration')}</TabsTrigger>}
            {integrationPermissions.canCreateWeb(
              backendUser?.permissions || []
            ) && <TabsTrigger value="web">{t('web-integration')}</TabsTrigger>}
          </TabsList>

          {/* Jira Integration Form */}
          <TabsContent value="jira" className="mt-6">
            <div className="bg-card rounded-lg border p-6 shadow-md">
              <h2 className="text-xl font-semibold mb-4">{t('jira-integration')}</h2>
              <p className="text-muted-foreground mb-6">
                {t('configure-your-jira-integration-by-providing-the-required-details-below')}
              </p>
              <Form {...jiraForm}>
                <form
                  onSubmit={jiraForm.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={jiraForm.control}
                      name="name"
                      render={({
                        field,
                      }: {
                        field: { value: any; onChange: (value: any) => void };
                      }) => (
                        <FormItem>
                          <FormLabel>{t('integration-name')}</FormLabel>
                          <FormControl>
                            <Input placeholder={t('product-backlog')} {...field} />
                          </FormControl>
                          <FormDescription>
                            {t('a-descriptive-name-for-this-integration')}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={jiraForm.control}
                      name="project"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('project-key')}</FormLabel>
                          <FormControl>
                            <Input placeholder={t('e-g-prd')} {...field} />
                          </FormControl>
                          <FormDescription>
                            {t('this-is-the-unique')} <strong>{t('project-key-0')}</strong>{" "}
                            {t('e-g-prd-not-the-full-project-name')}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={jiraForm.control}
                      name="token"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('api-token')}</FormLabel>
                          <FormControl>
                            <Input type="password" {...field} />
                          </FormControl>
                          <FormDescription>
                            {t('you-can-generate-a-jira-api-token-from-your-atlassian-account-settings')}{" "}
                            <a
                              href="https://id.atlassian.com/manage/api-tokens"
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:underline"
                            >
                              {t('get-your-token-here')}
                            </a>
                            .
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={jiraForm.control}
                      name="domain"
                      render={({
                        field,
                      }: {
                        field: { value: any; onChange: (value: any) => void };
                      }) => (
                        <FormItem>
                          <FormLabel>{t('domain-name')}</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="company.atlassian.net"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            {t('your-jira-instance-domain')}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={jiraForm.control}
                      name="email"
                      render={({
                        field,
                      }: {
                        field: { value: any; onChange: (value: any) => void };
                      }) => (
                        <FormItem>
                          <FormLabel>{t('email')}</FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="<EMAIL>"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            {t('email-associated-with-your-jira-account')}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={jiraForm.control}
                      name="updateTime"
                      render={({
                        field,
                      }: {
                        field: { value: any; onChange: (value: any) => void };
                      }) => (
                        <FormItem>
                          <FormLabel>{t('update-frequency-days')}</FormLabel>
                          <FormControl>
                            <Input type="number" min={1} {...field} />
                          </FormControl>
                          <FormDescription>
                            {t('how-often-to-sync-data-minimum-1-day')}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full"
                  >
                    {isSubmitting && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    <PlusCircle className="mr-2 h-4 w-4" />
                    {t('create-jira-integration')}
                  </Button>
                </form>
              </Form>
            </div>
          </TabsContent>

          {/* FTP Integration Form */}
          <TabsContent value="ftp" className="mt-6">
            <div className="bg-card rounded-lg border p-6 shadow-md">
              <h2 className="text-xl font-semibold mb-4">{t('ftp-integration-0')}</h2>
              <p className="text-muted-foreground mb-6">
                {t('configure-your-ftp-integration-by-providing-the-required-details-below')}
              </p>
              <Form {...ftpForm}>
                <form
                  onSubmit={ftpForm.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={ftpForm.control}
                      name="name"
                      render={({
                        field,
                      }: {
                        field: { value: any; onChange: (value: any) => void };
                      }) => (
                        <FormItem>
                          <FormLabel>{t('integration-name')}</FormLabel>
                          <FormControl>
                            <Input placeholder={t('financial-reports')} {...field} />
                          </FormControl>
                          <FormDescription>
                            {t('a-descriptive-name-for-this-integration-0')}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={ftpForm.control}
                      name="server"
                      render={({
                        field,
                      }: {
                        field: { value: any; onChange: (value: any) => void };
                      }) => (
                        <FormItem>
                          <FormLabel>{t('server-ip')}</FormLabel>
                          <FormControl>
                            <Input placeholder="ftp.example.com" {...field} />
                          </FormControl>
                          <FormDescription>{t('ftp-server-address')}</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={ftpForm.control}
                      name="port"
                      render={({
                        field,
                      }: {
                        field: { value: any; onChange: (value: any) => void };
                      }) => (
                        <FormItem>
                          <FormLabel>{t('port')}</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormDescription>
                            {t('ftp-server-port-usually-21')}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={ftpForm.control}
                      name="username"
                      render={({
                        field,
                      }: {
                        field: { value: any; onChange: (value: any) => void };
                      }) => (
                        <FormItem>
                          <FormLabel>{t('username')}</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormDescription>
                            {t('ftp-account-username')}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={ftpForm.control}
                      name="password"
                      render={({
                        field,
                      }: {
                        field: { value: any; onChange: (value: any) => void };
                      }) => (
                        <FormItem>
                          <FormLabel>{t('password')}</FormLabel>
                          <FormControl>
                            <Input type="password" {...field} />
                          </FormControl>
                          <FormDescription>
                            {t('ftp-account-password-stored-securely')}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={ftpForm.control}
                      name="isSecure"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('connection-type')}</FormLabel>
                          <Select
                            onValueChange={(value) =>
                              field.onChange(value === "true")
                            }
                            defaultValue={field.value ? "true" : "false"}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder={t('select-connection-type')} />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="false">
                                {t('ftp-not-encrypted')}
                              </SelectItem>
                              <SelectItem value="true">
                                {t('sftp-secure')}
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            {t('choose-between-ftp-and-secure-sftp')}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={ftpForm.control}
                      name="updateTime"
                      render={({
                        field,
                      }: {
                        field: { value: any; onChange: (value: any) => void };
                      }) => (
                        <FormItem>
                          <FormLabel>{t('update-frequency-days-0')}</FormLabel>
                          <FormControl>
                            <Input type="number" min={1} {...field} />
                          </FormControl>
                          <FormDescription>
                            {t('how-often-to-sync-data-minimum-1-day')}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full"
                  >
                    {isSubmitting && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    <PlusCircle className="mr-2 h-4 w-4" />
                    {t('create-ftp-integration')}
                  </Button>
                </form>
              </Form>
            </div>
          </TabsContent>

          {/* Web Integration Form */}
          <TabsContent value="web" className="mt-6">
            <div className="bg-card rounded-lg border p-6 shadow-md">
              <h2 className="text-xl font-semibold mb-4">{t('web-integration-0')}</h2>
              <p className="text-muted-foreground mb-6">
                {t('configure-your-web-integration-by-providing-the-required-details-below')}
              </p>
              <Form {...webForm}>
                <form
                  onSubmit={webForm.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={webForm.control}
                      name="name"
                      render={({
                        field,
                      }: {
                        field: { value: any; onChange: (value: any) => void };
                      }) => (
                        <FormItem>
                          <FormLabel>{t('integration-name')}</FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t('competitor-analysis')}
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            {t('a-descriptive-name-for-this-integration-1')}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={webForm.control}
                      name="url"
                      render={({
                        field,
                      }: {
                        field: { value: any; onChange: (value: any) => void };
                      }) => (
                        <FormItem>
                          <FormLabel>URL</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="https://example.com/data"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            {t('the-web-url-to-fetch-data-from')}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={webForm.control}
                      name="updateTime"
                      render={({
                        field,
                      }: {
                        field: { value: any; onChange: (value: any) => void };
                      }) => (
                        <FormItem>
                          <FormLabel>{t('update-frequency-days')}</FormLabel>
                          <FormControl>
                            <Input type="number" min={1} {...field} />
                          </FormControl>
                          <FormDescription>
                            {t('how-often-to-sync-data-minimum-1-day')}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full"
                  >
                    {isSubmitting && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    <PlusCircle className="mr-2 h-4 w-4" />
                    {t('create-web-integration')}
                  </Button>
                </form>
              </Form>
            </div>
          </TabsContent>
        </Tabs>
      </div>
      {/* <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      /> */}

      {/* Usage Limit Dialog */}
      <UsageLimitDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        type={dialogType}
        limit={dialogLimit}
      />
    </>
  );
}
