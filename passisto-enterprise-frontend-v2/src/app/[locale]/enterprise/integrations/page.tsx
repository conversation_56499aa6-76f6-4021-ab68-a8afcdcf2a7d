"use client";

import { IntegrationsTable } from "@/components/integrations/integrations-table";
import  StorageProgressBar  from "@/components/integrations/storage-progress-bar"; // Import the progress bar
import { Button } from "@/components/ui/button";
import {
  Plus,
  Loader2,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Database,
  TrendingUp,
  Activity,
} from "lucide-react";
import { Link } from '@/i18n/nvigation';
import { useEffect, useState } from "react";
import { useAuth } from "@clerk/nextjs";
import { ALL_INTEGRATIONS } from "@/utils/routes";
import axiosInstance from "@/config/axios";
import { checkPermissions, integrationPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from "@/hooks/useBackendUser";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useTranslations } from "next-intl";

interface IntegrationMetrics {
  total: number;
  active: number;
  completed: number;
  failed: number;
}

export default function IntegrationsPage() {
  const t = useTranslations();
  const [metrics, setMetrics] = useState<IntegrationMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { getToken } = useAuth();
  const { backendUser, loading: backendUserLoading } = useBackendUser();
  const router = useRouter();


  useEffect(() => {
    if (!backendUserLoading && backendUser) {
      const permissions = backendUser?.permissions ?? [];
      const hasAnyIntegrationPermission = checkPermissions(permissions, [
        integrationPermissions.canView,
      ]);

      if (!hasAnyIntegrationPermission) {
        toast.error(t('permission-denied'), {
          description: t('you-dont-have-permission-to-access-the-integrations-page'),
        });
        router.back();
      }
    }
  }, [backendUser, backendUserLoading, router]);

  // Replace with your actual company ID or get it from context/params
  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        const token = await getToken();
        const headers = {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        };
        const { data } = await axiosInstance.get<IntegrationMetrics>(
          `${ALL_INTEGRATIONS}/metrics`,
          headers
        );
        setMetrics(data);
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : t('failed-to-fetch-integration-metrics')
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchMetrics();
  }, [getToken]);

  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col">
        <main className="flex-1 container py-6">
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <p className="ml-4 text-muted-foreground">
              {t('loading-integration-metrics')}
            </p>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen flex-col">
        <main className="flex-1 container py-6">
          <div className="flex items-center justify-center h-64">
            <p className="text-red-500">Error: {error}</p>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-950">
      <main className="flex-1 container py-10 px-4 md:px-8">
        {/* Header Section */}
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-10">
          <div>
            <h1 className="text-4xl font-extrabold text-gray-900 tracking-tight dark:text-white">
              {t('data-integrations')}
            </h1>
            <p className="mt-2 text-lg text-gray-600 dark:text-gray-400 max-w-md">
              {t('manage-and-connect-your-enterprise-data-from-a-variety-of-sources-all-in-one-place')}
            </p>
          </div>

          <Link href="/enterprise/integrations/new">
            {integrationPermissions.canCreateAll(
              backendUser?.permissions || []
            ) && (
              <Button className="mt-4 md:mt-0 bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-md hover:shadow-lg transition-all duration-300 hover:from-blue-700 hover:to-purple-700">
                <Plus className="mr-2 h-4 w-4" />
                {t('new-integration')}
              </Button>
            )}
          </Link>
        </div>

        {/* Dashboard Overview Section */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8 mb-8">
          
          {/* Left Column - Metrics Cards */}
          <div className="xl:col-span-2 space-y-6">
            <div className="flex items-center space-x-2 mb-4">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {t('integration-metrics')}
              </h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <IntegrationSummaryCard
                title={t('total-integrations')}
                value={metrics?.total.toString() || "0"}
                description={t('active-data-connections')}
                icon={<Database className="h-6 w-6 text-blue-500" />}
              />
              <IntegrationSummaryCard
                title={t('active')}
                value={metrics?.active.toString() || "0"}
                description={t('currently-processing')}
                variant="running"
                icon={<RefreshCw className="h-6 w-6 text-yellow-500" />}
              />
              <IntegrationSummaryCard
                title={t('completed')}
                value={metrics?.completed.toString() || "0"}
                description={t('successfully-processed')}
                variant="completed"
                icon={<CheckCircle className="h-6 w-6 text-green-500" />}
              />
              <IntegrationSummaryCard
                title={t('failed-0')}
                value={metrics?.failed.toString() || "0"}
                description={t('needs-attention')}
                variant="failed"
                icon={<AlertCircle className="h-6 w-6 text-red-500" />}
              />
            </div>
          </div>

          {/* Right Column - Storage Progress Bar */}
          <div className="xl:col-span-1">
            <div className="sticky top-6">
              <div className="flex items-center space-x-2 mb-4">
                <Activity className="h-5 w-5 text-purple-600" />
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  {t('storage-usage')}
                </h2>
              </div>
              <div className="transform hover:scale-105 transition-transform duration-300">
                <StorageProgressBar />
              </div>
            </div>
          </div>
        </div>

        {/* Table Section */}
        <div className="bg-card rounded-xl border shadow-lg backdrop-blur-sm">
          <div className="p-6 border-b bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-900 rounded-t-xl">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-semibold text-primary flex items-center space-x-2">
                  <Database className="h-6 w-6 text-blue-600" />
                  <span>{t('all-integrations')}</span>
                </h2>
                <p className="text-sm text-muted-foreground mt-1">
                  {t('view-filter-and-manage-your-data-integrations')}
                </p>
              </div>
              <div className="hidden md:flex items-center space-x-2 text-sm text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>{t('completed')}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <span>{t('active')}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <span>{t('failed-0')}</span>
                </div>
              </div>
            </div>
          </div>
          <div className="p-6">
            <IntegrationsTable />
          </div>
        </div>
      </main>
    </div>
  );
}

interface IntegrationSummaryCardProps {
  title: string;
  value: string;
  description: string;
  variant?: "default" | "running" | "completed" | "failed";
  icon?: React.ReactNode;
}

function IntegrationSummaryCard({
  title,
  value,
  description,
  variant = "default",
  icon,
}: IntegrationSummaryCardProps) {
  const variantStyles = {
    default: "bg-white border-gray-200 dark:bg-gray-800 dark:border-gray-700 hover:shadow-lg",
    running:
      "bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200 dark:from-yellow-900 dark:to-orange-900 dark:border-yellow-800 hover:shadow-yellow-500/20",
    completed:
      "bg-gradient-to-br from-green-50 to-emerald-50 border-green-200 dark:from-green-900 dark:to-emerald-900 dark:border-green-800 hover:shadow-green-500/20",
    failed: "bg-gradient-to-br from-red-50 to-rose-50 border-red-200 dark:from-red-900 dark:to-rose-900 dark:border-red-800 hover:shadow-red-500/20",
  };

  const valueStyles = {
    default: "text-gray-800 dark:text-gray-200",
    running: "text-yellow-600 dark:text-yellow-400",
    completed: "text-green-600 dark:text-green-400",
    failed: "text-red-600 dark:text-red-400",
  };

  return (
    <div
      className={`rounded-xl border p-6 shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 ${variantStyles[variant]}`}
    >
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-muted-foreground">{title}</h3>
        <div className="p-2 bg-white/50 rounded-lg shadow-sm">
          {icon}
        </div>
      </div>
      <p className={`text-3xl font-bold mt-3 ${valueStyles[variant]}`}>
        {value}
      </p>
      <p className="text-xs text-muted-foreground mt-1">{description}</p>
    </div>
  );
}